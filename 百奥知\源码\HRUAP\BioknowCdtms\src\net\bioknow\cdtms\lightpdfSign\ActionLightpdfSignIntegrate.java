package net.bioknow.cdtms.lightpdfSign;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import net.bioknow.dbplug.wordreport.DAOWordreport;
import net.bioknow.dbplug.wordreport.UtilAsposeword;
import net.bioknow.mvc.RootAction;
import net.bioknow.mvc.tools.WebPath;
import net.bioknow.passport.datamng.DAOPPData;
import net.bioknow.passport.datamng.PassportCacheUtil;
import net.bioknow.services.core.ApiResult;
import net.bioknow.uap.dbcore.schema.CNT_Schema;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.schemaplug.attach.AttachDAO;
import net.bioknow.uap.schemaplug.dtref.DtrefDAO;
import net.bioknow.uapplug.dbview.DAODbview;
import net.bioknow.uapplug.dbview.DbviewUtil;
import net.bioknow.uapplug.publicworkflow.DAOPublicworkflow;
import net.bioknow.uapplug.publicworkflow.PublicworkflowUtil;
import net.bioknow.uapplug.usersyn.CNT_Usersyn;
import net.bioknow.uapplug.usersyn.DAOUsersyn;
import net.bioknow.webio.restful.Restful;
import net.bioknow.webplug.transemail.DAOTransemail;
import net.bioknow.webplug.transemail.ThreadSendmail;
import net.bioknow.webutil.fileup.DAOFileup;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.session.tokensession.BeanSessUser;
import net.bioknow.webutil.session.tokensession.UtilTokenSession;
import net.bioknow.webutil.timercache.TimerCacheUtil;
import net.bioknow.webutil.tools.Log;
import net.bioknow.webutil.tools.URLUtil;
import net.bioknow.webutil.tools.UUIDUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.DigestInputStream;
import java.security.MessageDigest;
import java.text.DateFormat;
import java.text.DateFormatSymbols;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.jpush.api.push.model.PushModel.gson;
import static net.bioknow.cdtms.lightpdfSign.DAOLightpdfSignIntegrate.esignUrl;
import static net.bioknow.cdtms.lightpdfSign.DAOLightpdfSignIntegrate.sasOnlieApiPrefix;
import static net.bioknow.cdtms.lightpdfSign.ResponseUtils.response;
import static net.bioknow.cdtms.lightpdfSign.WaterMarkUtils.PDFAddWatermark;
import static org.apache.commons.lang3.StringUtils.contains;


public class ActionLightpdfSignIntegrate extends RootAction {


    private static final Map mapCodeCache = new HashMap();
    private static final Map mapCodeTimeCache = new HashMap();

    private static String generateRandomCode() {
        String str = "0123456789";
        char[] chars = str.toCharArray();
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < 6; i++) {
            int index = (int) (Math.random() * chars.length);
            sb.append(chars[index]);
        }
        return sb.toString();


    }

    private String getDateTimeEn(Date dateTime) {
        try {
            Calendar instance = Calendar.getInstance();
            instance.setTime(dateTime);
            int month = instance.get(Calendar.MONTH);
            DateFormatSymbols dateFormatSymbols = new DateFormatSymbols(Locale.ENGLISH);
            String[] months = dateFormatSymbols.getMonths();
            String format = new SimpleDateFormat("hh:mm a", new DateFormatSymbols(Locale.ENGLISH)).format(dateTime);
            return format + " on " + months[month] + " " + instance.get(Calendar.DATE) + ", " + instance.get(Calendar.YEAR);
        } catch (Exception e) {
            Log.error("", e);
        }
        return "";
    }

    private String getlocStr(HttpServletRequest request) {

        String locStr = null;

        Cookie[] ckA = request.getCookies();
        if (ckA != null) {
            for (int i = 0; i < ckA.length; ++i) {
                Cookie ck = ckA[i];
                String ckname = ck.getName();
                if (ckname != null && ckname.equalsIgnoreCase("signlang")) {
                    String locStrInCookie = ck.getValue().toLowerCase();
                    if (locStrInCookie != null && !locStrInCookie.equals("")) {
                        locStr = locStrInCookie;
                        break;
                    }
                }
            }
        }

        if (locStr == null) {
            locStr = "cn";
        }

        return locStr;


    }


    public void jumpUrl(HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            String key = request.getParameter("key");
            List<String> path = (List<String>) TimerCacheUtil.get(key);
            StringBuilder builder = new StringBuilder();

            for (String string : path) {
                builder.append("<script>");
                builder.append("window.location.reload();");
                builder.append("window.open('").append(string).append("');");
                builder.append("</script>");
            }

            response.getOutputStream().write(builder.toString().getBytes(StandardCharsets.UTF_8));
            response.getOutputStream().close();
        } catch (Exception e) {
            Log.error("", e);
        }
    }

    //撤销签字任务
    public void Revoke(HttpServletRequest request, HttpServletResponse response) {

        try {

            String revokeReson = request.getParameter("revokeReson");
            boolean isVue=false;
            if (StringUtils.isEmpty(revokeReson)) {

                isVue=true;
                String ReceiveMsg = request.getParameter("value");

//                InputStream is = request.getInputStream();
//                String ReceiveMsg = IOUtils.toString(is, StandardCharsets.UTF_8);
//                is.close();

                GsonBuilder gsonBuilder = new GsonBuilder();
                gsonBuilder.registerTypeAdapter(new TypeToken<Map<String, Object>>() {
                }.getType(), new MapDeserializerDoubleAsIntFix());
                Gson gson = gsonBuilder.create();
                Map<String, Object> ReceiveMap = gson.fromJson(ReceiveMsg, new TypeToken<Map<String, Object>>() {
                }.getType());

                revokeReson = (String) ReceiveMap.get("revokeReson");
            }

            String esignInstanceIdArrStr = request.getParameter("esignInstanceId");
            String[] esignInstanceIdArr = esignInstanceIdArrStr.split(",");

            String projectId = SessUtil.getSessInfo().getProjectid();
            Date currentDate = new Date();

            String projectid = SessUtil.getSessInfo().getProjectid();

            DAODataMng daoDataMng = new DAODataMng(projectid);
            GsonBuilder gsonBuilder = new GsonBuilder();
            gsonBuilder.registerTypeAdapter(new TypeToken<Map<String, Object>>() {
            }.getType(), new MapDeserializerDoubleAsIntFix());
            Gson gson = gsonBuilder.create();
            Map currentUserMap = SessUtil.getSessInfo().getUser();


            Map revokeParamMap = new HashMap<>();
            revokeParamMap.put("type", "2");
            revokeParamMap.put("remark", revokeReson);
            String revokeParamJson = gson.toJson(revokeParamMap);

            List<Map<String, String>> listR = DAOLightpdfSignIntegrate.listRule(projectId, "1");

            String esignUrl = listR.get(0).get(DAOLightpdfSignIntegrate.esignUrl);
            for (String esignInstanceIdStr : esignInstanceIdArr) {


                Long esignInstanceId = Long.valueOf(esignInstanceIdStr);

                Map esignInstanceMap = daoDataMng.getRecord("esign_instance", esignInstanceId);

                String signFlowId = (String) esignInstanceMap.get("sign_flow_id");
                String language = esignInstanceMap.get("language") == null ? "cn" : esignInstanceMap.get("language").toString();

                String revokeMsg = LightpdfSignIntegrateUtil.httpPut(esignUrl + "/api/tasks/" + signFlowId, revokeParamJson);

                DAOTransemail daoTransemail = new DAOTransemail(projectId);

                String initiatorMail = PassportCacheUtil.getUserLoginidById(projectId, String.valueOf(esignInstanceMap.get("userid")));


                Map esignLogToSaveMap = new HashMap<>();
                esignLogToSaveMap.put("esign_instance_id", esignInstanceId);
                esignLogToSaveMap.put("tableid", esignInstanceMap.get("tableid"));
                esignLogToSaveMap.put("recordid", esignInstanceMap.get("recordid"));
                esignLogToSaveMap.put("study_id", esignInstanceMap.get("study_id"));
                esignLogToSaveMap.put("esign_status", "7");
                esignLogToSaveMap.put("execute_date", currentDate);


                esignLogToSaveMap.put("name", currentUserMap.get("username"));

                Map esignInstanceToSaveMap = new HashMap<>();

                esignInstanceToSaveMap.put("id", esignInstanceId);
                esignInstanceToSaveMap.put("revoke_msg", revokeMsg);
                esignInstanceToSaveMap.put("status", "5");
                esignInstanceToSaveMap.put("cancel_cause", revokeReson);
                esignInstanceToSaveMap.put("cancelor", currentUserMap.get("username"));
                esignInstanceToSaveMap.put("esign_end_date", currentDate);

                daoDataMng.save("esign_instance", esignInstanceToSaveMap);
                daoDataMng.save("esign_log", esignLogToSaveMap);

                List<Map<String, Object>> esignFileList = daoDataMng.listRecord("esign_file", "obj.esign_instance_id='" + esignInstanceId + "'", null, 100);
                Map<String, Object> esignFileMap = esignFileList.get(0);
                String initFileIndex = (String) esignFileMap.get("file");
                String initFileName = initFileIndex.split("\\*")[0];
                initFileName = initFileName.replace(".pdf", "");
                String content = "";
                if (StringUtils.equals(language, "en")) {
                    content = "The signature task for the \"" + initFileName + "\" has been abolished by the organizer, the reason for the abolition: " + revokeReson;
                } else {
                    content = "您好，《" + initFileName + "》该签署任务已被签署发起人废除。<br> 废除原因为" + revokeReson;
                }
                List<Map> esignSignerList = daoDataMng.listRecord("esign_signer", "obj.esign_instance_id=" + esignInstanceId +" and obj.status=0", null, 100);
//                StringBuilder receiver = new StringBuilder();
                List<String> receiver = new ArrayList<>();
                for (Map<String, String> esignSignerMap : esignSignerList) {
                    String email = esignSignerMap.get("user_code");
//                    if (receiver.length() > 0) {
//                        receiver.append(", ");
//                    }

                    ThreadSendmail.addTask(projectId, esignInstanceMap.get("subject") + "签署废除！", content, email);

                }

//                ThreadSendmail.addTask(projectId,esignInstanceMap.get("subject") + "签署废除！", content, receiver.toString());

            }

            if(isVue){

                response.getOutputStream().write(ApiResult.ok("办理成功！").getBytes(StandardCharsets.UTF_8));
                response.getOutputStream().close();
            }else{
                response.getOutputStream().write("200".getBytes(StandardCharsets.UTF_8));
                response.getOutputStream().close();
            }




        } catch (Exception e) {
            Log.error("", e);
        }

    }
    //签字提醒
    public void Urging(HttpServletRequest request, HttpServletResponse response) {

        try {


            String singerid = request.getParameter("id");

            String projectid = SessUtil.getSessInfo().getProjectid();


            List<Map<String, String>> DAOLightpdfSignIntegrateList = DAOLightpdfSignIntegrate.listRule(projectid, "1");

            Map<String, String> DAOLightpdfSignIntegrateMap = DAOLightpdfSignIntegrateList.get(0);

            String CDTMSUrl = DAOLightpdfSignIntegrateMap.get(DAOLightpdfSignIntegrate.CDTMSUrl);
            DAODataMng daoDataMng = new DAODataMng(projectid);

            DAOTransemail daoTransemail = new DAOTransemail(projectid);


            String content = null;

            Map esignSignerMap = daoDataMng.getRecord("esign_signer", Long.valueOf(singerid));

            String email = (String) esignSignerMap.get("user_code");

            Map esignInstanceMap = daoDataMng.getRecord("esign_instance", (Long) esignSignerMap.get("esign_instance_id"));

            String language = (String) esignInstanceMap.get("language");

            String fileName = (String) esignInstanceMap.get("file_name");

            String initiator = (String) esignInstanceMap.get("initiator");

            String backProcessId = (String) esignInstanceMap.get("sign_flow_id");

            Date signFlowExpireDate = (Date) esignInstanceMap.get("sign_expire_data");
            SimpleDateFormat DateFormatYMDHMS = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String signFlowExpireTime = DateFormatYMDHMS.format(signFlowExpireDate);
            //签字人
            String signerName = esignSignerMap.getOrDefault(CNT_Schema.name, "").toString();
            if (StringUtils.equals(language, "en")) {


                content = "Hello " + signerName +
                        "<br>You have a document \"" + fileName.replace(".pdf", "") + "\" need to be signed before " + signFlowExpireTime +
                        "<br>" +
                        "Signature CAPTCHA is " + esignSignerMap.get("verification_code")  +
                        "<br><br><a href=\"" + CDTMSUrl + "/lightpdfSign.checkIsLogin.do?system=" + projectid + "&task_id=" + backProcessId + "&email=" +
                        new String(Base64.getEncoder().encode(email.getBytes())) + "\" title=\"go to sign\">Go to sign</a><br><br><br>" +
                        "Initiator: " + initiator +
                        "<br><span style=\"font-size='9px'\">This email is sent by the system, please do not reply</span>"
                        + "<br><br><a href=\"https://cdtms.hengrui.com/webutil/js/pdfjs/web/viewer.show.do?file=/%E7%BA%BF%E4%B8%8A%E7%AD%BE%E5%90%8D%E5%8A%9F%E8%83%BD%E6%93%8D%E4%BD%9C%E6%8C%87%E5%8D%97_20231101.pdf&amp;unloadstr=\" target=\"_blank\" style=\"font-weight: bold;color: red;\">Signature operation guide</a>";


            } else {

                content = "您好，" + signerName + "<br> " +
                        "您有一份文件 《" + fileName.replace(".pdf", "") + "》 需要签署，请于" + signFlowExpireTime + "前完成。<br>" +
                        "签字验证码:" + esignSignerMap.get("verification_code") +
                        "<br><br><a href=\"" + CDTMSUrl + "/lightpdfSign.checkIsLogin.do?system=" + projectid + "&task_id=" + backProcessId + "&email=" +
                        new String(Base64.getEncoder().encode(email.getBytes())) + "&language=" + language + "\" title=\"去签署\">去签署</a><br><br><br>" +
                        "签字发起人：" + initiator +
                        "<br><span style=\"font-size='9px'\">该邮件为系统发出，请勿回复</span>"
                        + "<br><br><a href=\"https://cdtms.hengrui.com/webutil/js/pdfjs/web/viewer.show.do?file=/%E7%BA%BF%E4%B8%8A%E7%AD%BE%E5%90%8D%E5%8A%9F%E8%83%BD%E6%93%8D%E4%BD%9C%E6%8C%87%E5%8D%97_20231101.pdf&amp;unloadstr=\" target=\"_blank\" style=\"font-weight: bold;color: red;\">在线签署操作指南</a><br><br>";
            }

            ThreadSendmail.addTask(projectid, "《" + fileName.replace(".pdf", "") + "》 need to be signed!", content, email);
            ResponseUtils.response(response, HttpStatus.OK);
            return;

        } catch (Exception e) {
            e.printStackTrace();
        }


    }
    //查看签名文档
    public void View(HttpServletRequest request, HttpServletResponse response) throws IOException {

        try {
            String projectId = SessUtil.getSessInfo().getProjectid();
            DAODataMng daoDataMng = new DAODataMng(projectId);
            String tableid = request.getParameter("tableid");
            Map currentUserMap = SessUtil.getSessInfo().getUser();

            Long recordid = Long.valueOf(request.getParameter("recordid"));
            List esignInstanceList = daoDataMng.listRecord("esign_instance", "obj.active =1 and obj.tableid='" + tableid + "' and obj.recordid='" + recordid + "'", null, 1);
            List<Map<String, Object>> esignLogList = daoDataMng.listRecord("esign_log", "obj.tableid='" + tableid + "' and obj.recordid='" + recordid + "'", "obj.execute_date asc", 100);
            SimpleDateFormat sdf_d = new SimpleDateFormat("yyyy-MM-dd");

//            for (Map<String, Object> esignLogMap : esignLogList) {
//                 LightpdfSignIntegrateUtil.getRecordUI("esign_log", esignLogMap);
//            }

            for (int i = 0; i < esignLogList.size(); i++) {

                Map<String, Object> esignLogMap = esignLogList.get(i);
                esignLogMap.put("sn", i + 1);

                DateFormat formatTo = new SimpleDateFormat("HH:mm");
                if (ObjectUtils.isEmpty(esignLogMap.get("execute_date"))) continue;
                esignLogMap.put("timeStamp", formatTo.format(esignLogMap.get("execute_date")));

                Integer esignStatus = Integer.valueOf((String) esignLogMap.get("esign_status"));

                esignLogMap.put("name", StringUtils.equals((CharSequence) esignLogMap.get("name"), "SA") ? "" : esignLogMap.get("name"));

                switch (esignStatus) {
                    case 0:
                        esignLogMap.put("desc", "创建签署流程");
                        break;
                    case 1:
                        esignLogMap.put("desc", "签署完成");
                        break;
                    case 2:
                        esignLogMap.put("desc", "所有用户均已签署，签署成功");
                        break;
                    case 3:
                        esignLogMap.put("desc", "到达过期日期，已过期");
                        break;
                    case 4:
                        esignLogMap.put("desc", "签署截止前通知");
                        break;
                    case 5:
                        esignLogMap.put("desc", "等待设置签字位置");
                        break;
                    case 6:
                        esignLogMap.put("desc", "废除文件下载成功");
                        break;
                    case 7:
                        esignLogMap.put("desc", "已废除");
                        break;
                    default:
                        esignLogMap.put("desc", "");
                }
            }

            esignLogList.sort(Comparator.comparing((Map<String, Object> esignLogMap) -> (Integer) esignLogMap.get("sn")).reversed());

            Map<String, List<Map<String, Object>>> esignLogGroupDayMapAsc = esignLogList.stream().filter((Map esignLogMap) -> ObjectUtils.isNotEmpty(esignLogMap.get("execute_date"))).collect(Collectors.groupingBy((Map esignLogMap) -> sdf_d.format(esignLogMap.get("execute_date"))));

            Map esignLogGroupDayMap = new TreeMap<>(Collections.reverseOrder());

            esignLogGroupDayMap.putAll(esignLogGroupDayMapAsc);


            Map esignInstanceMap = (Map) esignInstanceList.get(0);
            Map esignInstanceUIMap = LightpdfSignIntegrateUtil.getRecordUI("esign_instance", esignInstanceMap, projectId);
            Long userid = (Long) esignInstanceMap.get("userid");
            DAOPPData dao = new DAOPPData(projectId);
            String initiatorName = dao.getUserById(userid).getRealName();

            SimpleDateFormat sdf_s = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            esignInstanceUIMap.put("initiatDate", sdf_s.format(esignInstanceMap.get("createtime")));


            Long esignInstanceId = (Long) esignInstanceMap.get("id");
            String Msg = (String) esignInstanceMap.get("esign_msg");

            GsonBuilder gsonBuilder = new GsonBuilder();
            gsonBuilder.registerTypeAdapter(new TypeToken<Map<String, Object>>() {
            }.getType(), new MapDeserializerDoubleAsIntFix());
            Gson gson = gsonBuilder.create();

            Map CNSMsgMap = gson.fromJson(Msg, new TypeToken<Map<String, Object>>() {
            }.getType());
//			Map msgData = (Map) CNSMsgMap.get("data");
//			List<Map> signUrlInfosList = (List) msgData.get("signUrlInfos");
//			Map<String, Map> signUrlInfosListToMap = signUrlInfosList.stream().collect(Collectors.toMap((Map map) -> (String) map.get("user_code"),t -> t));
//


            List<Map<String, Object>> esignFileList = daoDataMng.listRecord("esign_file", "obj.esign_instance_id=" + esignInstanceId, null, 100);

//            esignInstanceUIMap.put("esign_url", sdf_s.format(esignInstanceMap.get("createtime")));

//			List<Map<String, Object>> esignFileSignerList = daoDataMng.listRecord("esign_file_signer", "obj.esign_instance_id=" + esignInstanceId, null, 100);
            List<Map<String, Object>> esignSignerList = daoDataMng.listRecord("esign_signer", "obj.esign_instance_id=" + esignInstanceId, null, 100);

//            Map<String, Map> esignSignerListToMap = esignSignerList.stream().collect(Collectors.toMap((Map map) -> (String) map.get("user_code"), t -> t));

//            esignInstanceUIMap.put("Signer", esignSignerListToMap.get(currentUserMap.get("domain_accounts")));


//			for (Map<String, Object> esignFileSignerMap : esignFileSignerList) {
//				esignFileSignerMap.put("SignerInfoMap",esignSignerListToMap.get(esignFileSignerMap.get("user_code")));
//
//			}

//			Map<Long, List<Map<String, Object>>> esignFileSignerListToMap = esignFileSignerList.stream().collect(Collectors.groupingBy((Map map1) -> (Long) map1.get("esign_file_id")));


            for (Map esignFileMap : esignFileList) {


                String file = (String) esignFileMap.get("file");
                String signedFile = (String) esignFileMap.get("signed_file");

                String fileIndex = StringUtils.isNotEmpty(signedFile) ? signedFile : file;

                esignFileMap.put("filename", fileIndex.split("\\*")[0]);
//				eSignFileMap.put("filename",URLEncoder.encode(fileIndex.substring(0, fileIndex.lastIndexOf("*")),"UTF-8"));


                esignFileMap.put("file_uuid", fileIndex.substring(fileIndex.lastIndexOf("*") + 1, fileIndex.lastIndexOf("|")));


            }


            List esignSignerUIList = new ArrayList<>();
            for (Map esignSignertMap : esignSignerList) {
                esignSignerUIList.add(LightpdfSignIntegrateUtil.getRecordUI("esign_signer", esignSignertMap, projectId));
            }

//			String str = studyid_Schema.formatToOutput(tableid, mapF, eSignDataMap);
            request.setAttribute("esignInstanceMap", esignInstanceUIMap);
            request.setAttribute("esignFileList", esignFileList);
            request.setAttribute("esignLogGroupDayMap", esignLogGroupDayMap);
            request.setAttribute("fileSignerList", esignSignerUIList);


            this.forward(request, response, "View");


        } catch (Exception e) {
            Log.error("", e);
        } finally {


        }
    }

    public void ajaxCaneclPage(HttpServletRequest request, HttpServletResponse response) {

        request.setAttribute("id", request.getParameter("id"));
        this.forward(request, response, "ajaxCaneclPage");
    }

    public void ajaxsetSigner(HttpServletRequest request, HttpServletResponse response) {

        request.setAttribute("id", request.getParameter("id"));
        this.forward(request, response, "ajaxsetregister");
        return;
    }



    public void ajaxsetSigner3(HttpServletRequest request, HttpServletResponse response) {

        request.setAttribute("id", request.getParameter("id"));
        this.forward(request, response, "setSigner");
        return;
    }

    public void jump(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String recordId=request.getParameter("recordid");
        String tableId=request.getParameter("tableId");
        String projectid = SessUtil.getSessInfo().getProjectid();
        DAODataMng daoDataMng = new DAODataMng(projectid);

        List esignFileList = daoDataMng.listRecord("esign_file", "obj.esign_instance_id=" +recordId, null, 1);

        Map esignInstanceRecordMap = daoDataMng.getRecord(tableId, Long.valueOf(recordId));
        String signFlowId = (String) esignInstanceRecordMap.get("sign_flow_id");
        Map esignFileMap = (Map) esignFileList.get(0);
        String eSignfileKey = (String) esignFileMap.get("esign_file_key");
        List<Map<String, String>> listR = DAOLightpdfSignIntegrate.listRule(projectid, "1");
        String esignUrl = listR.get(0).get(DAOLightpdfSignIntegrate.esignUrl);

        String redirectUrl = esignUrl+"/set-sign?file_id="+eSignfileKey+"&task_id="+signFlowId;
        request.setAttribute("setUrl", redirectUrl);
//        this.redirectByUrl(request,response,redirectUrl);
        this.forward(request, response, "signPosJump");
        return;
    }

    public void ajaxsetSigner2(HttpServletRequest request, HttpServletResponse response) {
        try {
            request.setAttribute("id", request.getParameter("id"));

            String id = request.getParameter("id");
//            String projectid = SessUtil.getSessInfo().getProjectid();
//
//            DAODataMng daoDataMng = new DAODataMng(projectid);
//
//
//            List<Map> esignSignerList = daoDataMng.listRecord("esign_signer", "obj.esign_instance_id=" + id +" and obj.status in ('0','1','5')", null, 1000);
//
//            ArrayList<Object> selectedSignerList = new ArrayList<>();
//            for (Map esignSignerMap : esignSignerList) {
//
//                HashMap<Object, Object> selectedSignerListMap = new HashMap<>();
//                String name = (String) esignSignerMap.get("name");
//                String email = (String) esignSignerMap.get("user_code");
//
//                selectedSignerListMap.put("Name", name + "<" + email + ">");
//                selectedSignerListMap.put("Value", name + "<" + email + ">");
//                selectedSignerList.add(selectedSignerListMap);
//            }
//
//            Gson gson = new Gson();
//
//
//            request.setAttribute("selectedSignerJson", gson.toJson(selectedSignerList));


            this.forward(request, response, "setSigner");
            return;


        } catch (Exception e) {
            Log.error("err", e);
        }
    }

    public synchronized void callback(HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {


            InputStream is = request.getInputStream();
            String ReceiveMsg = IOUtils.toString(is, StandardCharsets.UTF_8);
            is.close();
            Log.info("---------------------------------------------------------回调接口中返回的内容是："+ReceiveMsg);

//            System.out.println(ReceiveMsg);
            HashMap<String, Object> MsgnMap = new HashMap<>();

            if (StringUtils.isBlank(ReceiveMsg)) {
                MsgnMap.put("msg", "无效的请求");
                response(response, HttpStatus.BAD_REQUEST);
                return;
            }

            String projectId = request.getParameter("projectid");
            DAODataMng daoDataMng = new DAODataMng(projectId);

            GsonBuilder gsonBuilder = new GsonBuilder();
            gsonBuilder.registerTypeAdapter(new TypeToken<Map<String, Object>>() {
            }.getType(), new MapDeserializerDoubleAsIntFix());
            Gson gson = gsonBuilder.create();
            Map<String, Object> ReceiveMap = gson.fromJson(ReceiveMsg, new TypeToken<Map<String, Object>>() {
            }.getType());

            Integer callBackEnum = (Integer) ReceiveMap.get("status");
            Log.info("---------------------------------------------------------回调接口中返回的任务处理状态是："+callBackEnum);
            //回调有三种类型：通过type参数区分
            //1. 用户签署回调
            //2. 文件签署回调 & 过期无签署
            //3. 任务下发回调（用于通知邮件发送）
            Integer callBackType = (Integer) ReceiveMap.get("type");
            String callBackDesc = (String) ReceiveMap.get("callBackDesc");
            String backProcessId = (String) ReceiveMap.get("task_id");

            List esignInstanceList = daoDataMng.listRecord("esign_instance", "obj.sign_flow_id='" + backProcessId + "'", null, 1);

            if (CollectionUtils.isEmpty(esignInstanceList)) {

                MsgnMap.put("msg", "无效的请求");
                response(response, HttpStatus.BAD_REQUEST);
                return;
            }

            response(response, HttpStatus.OK);


            Map esignInstanceMap = (Map) esignInstanceList.get(0);

            Long esignInstanceId = (Long) esignInstanceMap.get("id");
            String tableid = (String) esignInstanceMap.get("tableid");
            Long recordid = (Long) esignInstanceMap.get("recordid");
            Long studyid = (Long) esignInstanceMap.get("study_id");


            Map esignInstanceToSaveMap = new HashMap<>();
            esignInstanceToSaveMap.put("id", esignInstanceId);


            Map esignLogToSaveMap = new HashMap<>();
            esignLogToSaveMap.put("esign_instance_id", esignInstanceId);
            esignLogToSaveMap.put("tableid", tableid);
            esignLogToSaveMap.put("recordid", recordid);
            esignLogToSaveMap.put("study_id", studyid);
            esignLogToSaveMap.put("msg", ReceiveMsg);
            esignLogToSaveMap.put("esign_desc", callBackDesc);


//            List<Map<String, Object>> signerList = (List) callBackProcessVOMap.get("signerList");
//            Map<String, Object> signerMap = signerList.stream().max(Comparator.comparing((Map map) -> map.get("signDate") == null ? "" : (String) map.get("signDate"))).get();
//            esignInstanceToSaveMap.put("version", esignInstanceVersion);
            Map esignSignerToSaveMap = new HashMap<>();


            Integer backprocessEndTime = (Integer) ReceiveMap.get("updated_at");
            Date executeDate = null;
            if (backprocessEndTime != null) {
                executeDate = new Date(backprocessEndTime * 1000l);
            }


            DAOTransemail daoTransemail = new DAOTransemail(projectId);
            String subject = (String) esignInstanceMap.get("subject");

            String outputUrl = (String) ReceiveMap.get("output_url");
            Log.info("---------------------------------------------------------回调接口中返回的url是："+outputUrl);

            List<Map<String, Object>> esignFileList = daoDataMng.listRecord("esign_file", "obj.esign_instance_id='" + esignInstanceId + "'", null, 100);
            Map<String, Object> esignFileMap = esignFileList.get(0);
            String initFileIndex = (String) esignFileMap.get("file");
//            String initFileName = initFileIndex.split("\\*")[0];
//            initFileName = initFileName.replace(".pdf", "");
            String initFileName = (String) esignInstanceMap.get("file_name");
            initFileName = initFileName.replace(".pdf", "");

            Map<String, String> saveFileInfo = null;

            List<Map<String, String>> listR = DAOLightpdfSignIntegrate.listRule(projectId, "1");
            String esignUrl = listR.get(0).get(DAOLightpdfSignIntegrate.esignUrl);
            //添加会签查询，当签字类型是会签，只发送第一个人的邮件，在第一个人签署完成后，才发送其他人的邮件
            String orderSignMsg = LightpdfSignIntegrateUtil.httpGet(esignUrl + "/api/tasks/" + backProcessId);
            JSONObject orderSignMsgMap = JSONObject.parseObject(orderSignMsg);
            String data=orderSignMsgMap.get("data").toString();
            JSONObject dataObj = JSONObject.parseObject(data);
            String items = dataObj.get("items").toString();
            JSONArray array = JSONArray.parseArray(items);
            int  signType=0;
            String subUrl="";
            if(array.size()>0){
                String item=  array.get(0).toString();
                JSONObject itemObj = JSONObject.parseObject(item);
                signType = itemObj.get("sign_type")==null?0:(int) itemObj.get("sign_type");
                subUrl = itemObj.get("output_url")==null?"":itemObj.get("output_url").toString();
            }

            if (callBackType == 3) {

                AttachDAO attachDAO = new AttachDAO(projectId);
                List<Map<String, String>> DAOLightpdfSignIntegrateList = DAOLightpdfSignIntegrate.listRule(projectId, "1");

                Map<String, String> DAOLightpdfSignIntegrateMap = DAOLightpdfSignIntegrateList.get(0);

                String CDTMSUrl = DAOLightpdfSignIntegrateMap.get(DAOLightpdfSignIntegrate.CDTMSUrl);

                List<Map> singerList = (List) ReceiveMap.get("items");
                for (Map signerInfoMap : singerList) {
                    String remark= (String) signerInfoMap.get("remark");
                    if(StringUtils.isNotEmpty(remark)&&remark.equals("changeEnName")){
                        return;
                    }
                }

                String fileSelected = (String) esignInstanceMap.get("selected_file");


                File[] attachFiles = null;
                String[] attachFileNames = null;
                if (StringUtils.isNotEmpty(fileSelected)) {
                    String[] fileSelectedArr = fileSelected.split(",");
                    attachFiles = new File[fileSelectedArr.length];
                    attachFileNames = new String[fileSelectedArr.length];
                    if (!ArrayUtils.isEmpty(fileSelectedArr)) {
                        for (int i = 0; i < fileSelectedArr.length; ++i) {
                            String[] fileInfoArr = fileSelectedArr[i].split("\\|\\|");
                            if (fileInfoArr.length == 3) {
                                attachFiles[i] = attachDAO.getFile(fileInfoArr[0], fileInfoArr[2]);
                                attachFileNames[i] = fileSelectedArr[i].split("\\|\\|")[1];
                            }else if(fileInfoArr.length == 4&& fileInfoArr[3].equals("attachment")){
                                try {
                                    Path originalPath = Paths.get(WebPath.getRootPath() + "/webutil/fileup/temp" + "/" + fileInfoArr[0]);
                                    Path targetPath = Paths.get(WebPath.getRootPath() + "/webutil/fileup/temp" + "/" +LightpdfSignIntegrateUtil.getFileNameFromPath(fileInfoArr[1]));
                                    // 移动/重命名文件
                                    Files.move(originalPath, targetPath);

                                    attachFiles[i] = targetPath.toFile();
                                    Log.info("------------拿到的文件是：" + attachFiles[i].getPath() + " 拿到的文件名是：" + attachFiles[i].getName());
                                    attachFileNames[i] = attachFiles[i].getName();
                                } catch (IOException e) {
                                    Log.error("文件重命名失败: " + e.getMessage());
                                    // 根据需要处理异常
                                }
                            } else {
                                attachFiles[i] = attachDAO.getFile(fileInfoArr[0], tableid);
                                attachFileNames[i] = fileSelectedArr[i].split("\\|\\|")[1];
                            }
                            //attachFiles[i] = attachDAO.getFile(fileSelectedArr[i].split("\\|\\|")[0], tableid);

                        }
                    }
                }


                Date signFlowExpireDate = (Date) esignInstanceMap.get("sign_expire_data");
                SimpleDateFormat DateFormatYMDHMS = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String signFlowExpireTime = DateFormatYMDHMS.format(signFlowExpireDate);

                String language = (String) esignInstanceMap.get("language");
                String fileName = (String) esignInstanceMap.get("file_name");
                String content2 = (String) esignInstanceMap.get("content");
                String initiator = (String) esignInstanceMap.get("initiator");




                // 会签模式下只处理第一个签署人，其他情况处理所有签署人
                for (Map signerInfoMap : singerList) {

                    String email = (String) signerInfoMap.get("email");
                    String signFlowId = (String) signerInfoMap.get("task_id");
                    String content = null;
                    String receiver = new String(Base64.getDecoder().decode(email.getBytes()));

                    Map esignSignerMap = (Map) daoDataMng.listRecord("esign_signer", "obj.esign_instance_id=" + esignInstanceMap.get("id") + " and obj.user_code='" + receiver + "' and obj.status=5", null, 1).get(0);

                    if (signType == 1 && !singerList.get(0).equals(signerInfoMap)) {
                        esignSignerMap.put("status", "-1");
                    }else{
                        esignSignerMap.put("status", "0");
                    }



                    daoDataMng.saveRecord("esign_signer", esignSignerMap);

//                    Object o = esignSignerList.get(0);
                    //签字人
                    String signerName="";
                    if(!ObjectUtils.isEmpty(esignSignerMap.getOrDefault(CNT_Schema.name, ""))){
                         signerName = esignSignerMap.getOrDefault(CNT_Schema.name, "").toString();
                    }

                    if (StringUtils.equals(language, "en")) {


                        content = "Hello " + signerName +
                                "<br>You have a document \"" + fileName.replace(".pdf", "") + "\" need to be signed before " + signFlowExpireTime +
                                "<br>" +
                                "Signature CAPTCHA is " + esignSignerMap.get("verification_code")  +
                                "<br><br><a href=\"" + CDTMSUrl + "/lightpdfSign.checkIsLogin.do?system=" + projectId + "&task_id=" + backProcessId + "&email=" +
                                email + "\" title=\"go to sign\">Go to sign</a><br><br><br>" +
                                "Initiator: " + initiator +
                                "<br><span style=\"font-size='9px'\">This email is sent by the system, please do not reply</span>"
                                + "<br><br><a href=\"https://cdtms.hengrui.com/webutil/js/pdfjs/web/viewer.show.do?file=/%E7%BA%BF%E4%B8%8A%E7%AD%BE%E5%90%8D%E5%8A%9F%E8%83%BD%E6%93%8D%E4%BD%9C%E6%8C%87%E5%8D%97_20231101.pdf&amp;unloadstr=\" target=\"_blank\" style=\"font-weight: bold;color: red;\">Signature operation guide</a>";


                    } else {

                        content = "您好，" + signerName + "<br> " +
                                "您有一份文件 《" + fileName.replace(".pdf", "") + "》 需要签署，请于" + signFlowExpireTime + "前完成。<br>" +
                                "签字验证码:" + esignSignerMap.get("verification_code") +
                                "<br><br><a href=\"" + CDTMSUrl + "/lightpdfSign.checkIsLogin.do?system=" + projectId + "&task_id=" + backProcessId + "&email=" +
                                email + "&language=" + language + "\" title=\"去签署\">去签署</a><br><br><br>" +
                                "签字发起人：" + initiator +
                                "<br><span style=\"font-size='9px'\">该邮件为系统发出，请勿回复</span>"
                                + "<br><br><a href=\"https://cdtms.hengrui.com/webutil/js/pdfjs/web/viewer.show.do?file=/%E7%BA%BF%E4%B8%8A%E7%AD%BE%E5%90%8D%E5%8A%9F%E8%83%BD%E6%93%8D%E4%BD%9C%E6%8C%87%E5%8D%97_20231101.pdf&amp;unloadstr=\" target=\"_blank\" style=\"font-weight: bold;color: red;\">在线签署操作指南</a><br><br>";
                    }


                    if (content2.contains("<p>祝好</p>")) {
                        // 如果包含，则在该内容前插入文本
                        content = content2.replace("<p>祝好</p>", content + "<p>祝好</p>");
                    } else {
                        content = content + content2;
                    }

//                    if (StringUtils.isNotEmpty(emailSender)) {
//                        String sendMailResult = ThreadSendmail.addTask(projectId,emailSender, subject, content2, receiver, null, currEmail, attachFiles, attachFileNames);
//                    }else {


                    //ThreadSendmail.addTask(projectId,"《" + fileName.replace(".pdf", "") + "》 need to be signed!", content, new String(Base64.getDecoder().decode(email.getBytes())));
                    if (signType == 1 && !singerList.get(0).equals(signerInfoMap)) {
                        continue;
                    }
                    ThreadSendmail.addTask(projectId, "《" + fileName.replace(".pdf", "") + "》 need to be signed!", content, new String(Base64.getDecoder().decode(email.getBytes())),attachFiles, attachFileNames);


//                    }


                }


            }


            if (callBackType == 1) {

                String emailEncode = (String) ReceiveMap.get("email");
                String email = new String(Base64.getDecoder().decode(emailEncode.getBytes()));
                if(signType==1){
                    //处理除了第一个签署人之外的签署人的邮件通知
                    List<Map<String, Object>> esignSignerList = daoDataMng.listRecord("esign_signer", "obj.esign_instance_id='" + esignInstanceId + "' and obj.status=-1", null, 50);
                    if(esignSignerList.size()>0){
                        processOrderSign(esignSignerList,daoDataMng,esignInstanceMap,backProcessId,projectId);
                    }

                }
                esignLogToSaveMap.put("execute_date", executeDate);

                List<Map<String, Object>> esignSignerList = daoDataMng.listRecord("esign_signer", "obj.esign_instance_id='" + esignInstanceId + "' and obj.user_code='" + email + "' and obj.status=0", null, 1);

                if (CollectionUtils.isNotEmpty(esignSignerList)) {
                    Map<String, Object> esignSignerMap = esignSignerList.get(0);
                    esignSignerToSaveMap.put("id", esignSignerMap.get("id"));
                    esignSignerToSaveMap.put("status", "2");
                    String vs = ReceiveMap.get("sign_time") == null || "".equals(ReceiveMap.get("sign_time")) ? "" : ReceiveMap.get("sign_time").toString();
                    Date executeDate1 = null;
                    if (!"".equals(vs)) {
                        executeDate1 = LightpdfSignIntegrateUtil.dateTimeFormat(vs);
                    }
                    esignSignerToSaveMap.put("execute_date", executeDate1);

                    esignLogToSaveMap.put("esign_status", "1");

                    esignLogToSaveMap.put("execute_date", executeDate1);
                    esignLogToSaveMap.put("name", esignSignerMap.get("name"));


                    String reason = (String) ReceiveMap.get("reason");

                    String sign_reason = null;
                    if (StringUtils.contains(reason, "作者")) sign_reason = "author";
                    if (StringUtils.contains(reason, "审核")) sign_reason = "review";
                    if (StringUtils.contains(reason, "批准")) sign_reason = "approve";
                    if (StringUtils.contains(reason, "负责")) sign_reason = "responsible";
                    if (StringUtils.contains(reason, "author")) sign_reason = "author";
                    if (StringUtils.contains(reason, "reviewed")) sign_reason = "review";
                    if (StringUtils.contains(reason, "approve")) sign_reason = "approve";
                    if (StringUtils.contains(reason, "responsible")) sign_reason = "responsible";
                    esignSignerToSaveMap.put("sign_reason", sign_reason);


                }


                daoDataMng.save("esign_signer", esignSignerToSaveMap);

                //cdtms
                String initiatorMail = PassportCacheUtil.getUserLoginidById(projectId, String.valueOf(esignInstanceMap.get("userid")));
                //一体化
//                String initiatorLoginid = PassportCacheUtil.getUserLoginidById(projectId, String.valueOf(esignInstanceMap.get("userid")));
//                DAOPPData daoppData = new DAOPPData(projectId);
//                String initiatorMail = daoppData.getUserByEmailOrLoginid(initiatorLoginid).getEmail();
                //拼接签字文件的链接
                String content = "您好，您提交的《" + initFileName + "》," + esignLogToSaveMap.get("name") + "已签署完成。";


                ThreadSendmail.addTask(projectId, initFileName + "," + esignLogToSaveMap.get("name") + "已签署完成", content, initiatorMail);


         /*       Map eSignEngineMap = daoDataMng.getRecord("esign_engine", (Long) esignInstanceMap.get("esign_engine_id"));

                String isFinishAutoProcessWorkflow = (String) eSignEngineMap.get("is_finish_wk_auto_process");
                if (StringUtils.equals(isFinishAutoProcessWorkflow,"1")) {
                    String todofuncid = getFuncidForward(projectId,tableid,String.valueOf(recordid));
                    PublicworkflowUtil.process(projectId, tableid, String.valueOf(recordid), todofuncid, null, null, null, null, "","");
                }*/

            }

            if (callBackEnum != null) {

                if (callBackEnum == 4) {

                    //流程完成业务

                    esignInstanceToSaveMap.put("esign_end_date", executeDate);
                    esignInstanceToSaveMap.put("status", "2");
                    esignLogToSaveMap.put("esign_status", "2");
                    esignLogToSaveMap.put("execute_date", executeDate);
                    esignLogToSaveMap.put("name", "SA");

                    if (StringUtils.isEmpty(outputUrl)) {
                        //cdtms
                        String initiatorMail = PassportCacheUtil.getUserLoginidById(projectId, String.valueOf(esignInstanceMap.get("userid")));
                        //一体化
//                        String initiatorLoginid = PassportCacheUtil.getUserLoginidById(projectId, String.valueOf(esignInstanceMap.get("userid")));
//                        DAOPPData daoppData = new DAOPPData(projectId);
//                        String initiatorMail = daoppData.getUserByEmailOrLoginid(initiatorLoginid).getEmail();
                        String content = "您好，您提交的《" + initFileName + "》已签署完成，文件返回失败，请联系管理员。";


                        ThreadSendmail.addTask(projectId, initFileName + "，文件返回失败！", content, initiatorMail);


                    } else {
                        saveFileInfo = saveBackFile(projectId, outputUrl, tableid, recordid, (Long) esignInstanceMap.get("esign_engine_id"), esignFileMap,callBackEnum);
                        List<Map> esignSignerList = daoDataMng.listRecord("esign_signer", "obj.esign_instance_id=" + esignInstanceMap.get("id") + " and obj.status='2'", null, 100);
                        //bug点  saveFileInfo.get("fileName");空指针异常
                        String fileName = saveFileInfo.get("fileName");
                        String filePath = saveFileInfo.get("filePath");
                        SimpleDateFormat sdf = new SimpleDateFormat("yyMMddHHmm");
                        String currDate = sdf.format(new Date());

                        int lastSeparatorIndex = filePath.lastIndexOf("/");
                        filePath.substring(lastSeparatorIndex + 1);
                       // String outFilePath = filePath.replaceAll(".pdf", "_Water.pdf");
                      //  PDFAddWatermark(filePath, outFilePath, "江苏恒瑞医药有限公司内部文件-" + currDate + "-副本", 80, 170, 12, 190, 200, 200);

                        File[] mailFile = {new File(filePath)};
                        String[] mailFileName = {fileName};
                        List<String> SignerMails = new ArrayList<>();
                        String language = (String) esignInstanceMap.get("language");
                        for (Map<String, Object> esignSignerMap : esignSignerList) {

                            SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                            Object execute_date = esignSignerMap.get("execute_date");
                            String signDate = "";

                            if (ObjectUtils.isNotEmpty(execute_date)) {
                                signDate = sdf2.format(execute_date);

                            }
                            String content = "";
                            if (StringUtils.equals(language, "en")) {
                                content = "Hello, The document \"" + initFileName + "\" you signed at " + getDateTimeEn(sdf2.parse(signDate)) + " has now been signed by all signatories.";
                            } else {
                                content = "您好，您于" + signDate + "签署的《" + initFileName + "》已签署完成。";

                            }
                            if(!subUrl.isEmpty()){
                                List<Map<String, String>> DAOLightpdfSignIntegrateList = DAOLightpdfSignIntegrate.listRule(projectId, "1");
                                Map<String, String> DAOLightpdfSignIntegrateMap = DAOLightpdfSignIntegrateList.get(0);
                                String CDTMSUrl = DAOLightpdfSignIntegrateMap.get(DAOLightpdfSignIntegrate.CDTMSUrl);
                                subUrl=CDTMSUrl+"/login.gotopage.do?projectid="+projectId+"&gotopage="+URLEncoder.encode(subUrl,"UTF-8");
                                if (StringUtils.equals(language, "en")) {
                                    content = content + "<br><br>please click<a href='" + subUrl + "'>here</a>to view.";
                                } else {
                                    content = content + "<br><br>请点击<a href='" + subUrl + "'>此处</a>查看。";

                                }

                            }
                            SignerMails.add((String) esignSignerMap.get("user_code"));
                            ThreadSendmail.addTask(projectId, initFileName + "签署完成！", content, (String) esignSignerMap.get("user_code"), mailFile, mailFileName);

                        }
                        //cdtms
                        String initiatorMail = PassportCacheUtil.getUserLoginidById(projectId, String.valueOf(esignInstanceMap.get("userid")));
                        //一体化
//                        String initiatorLoginid = PassportCacheUtil.getUserLoginidById(projectId, String.valueOf(esignInstanceMap.get("userid")));
//                        DAOPPData daoppData = new DAOPPData(projectId);
//                        String initiatorMail = daoppData.getUserByEmailOrLoginid(initiatorLoginid).getEmail();

                        if (!SignerMails.contains(initiatorMail)) {
                            String content = "您好，您提交的《" + esignInstanceMap.get("subject") + "》已签署完成。";
//                        ThreadSendmail.addTask(projectId,subject + "签署完成！", content, initiatorMail);
                            ThreadSendmail.addTask(projectId, initFileName + "签署完成！", content, initiatorMail, mailFile, mailFileName);
                        }
                    }

                    Map eSignEngineMap = daoDataMng.getRecord("esign_engine", (Long) esignInstanceMap.get("esign_engine_id"));
                    if(!MapUtils.isEmpty(eSignEngineMap)){
                        String isFinishAutoProcessWorkflow = (String) eSignEngineMap.get("is_finish_wk_auto_process");
                        if (StringUtils.equals(isFinishAutoProcessWorkflow,"1")) {
                            String todofuncid = getFuncidForward(projectId,tableid,String.valueOf(recordid));
                            PublicworkflowUtil.process(projectId, tableid, String.valueOf(recordid), todofuncid, null, null, null, null, "","");
                        }
                    }

                }

                if(callBackEnum == 1) {
                    if (!StringUtils.isEmpty(outputUrl)) {
                        saveFileInfo = saveBackFile(projectId, outputUrl, tableid, recordid, (Long) esignInstanceMap.get("esign_engine_id"), esignFileMap,callBackEnum);
                    }
                }

                if (callBackEnum == 2) {
                    esignInstanceToSaveMap.put("status", "3");
                    esignLogToSaveMap.put("esign_status", "3");


                    esignLogToSaveMap.put("execute_date", executeDate);
                    esignLogToSaveMap.put("name", "SA");


                    esignInstanceToSaveMap.put("esign_end_date", new Date(backprocessEndTime * 1000L));

                    //cdtms
                    String initiatorMail = PassportCacheUtil.getUserLoginidById(projectId, String.valueOf(esignInstanceMap.get("userid")));
                    //一体化
//                    String initiatorLoginid = PassportCacheUtil.getUserLoginidById(projectId, String.valueOf(esignInstanceMap.get("userid")));
//                    DAOPPData daoppData = new DAOPPData(projectId);
//                    String initiatorMail = daoppData.getUserByEmailOrLoginid(initiatorLoginid).getEmail();
                    String content = "您好，您提交的《" + initFileName + "》签署未完成并已过期，请补签后生效。";
                    ThreadSendmail.addTask(projectId, subject + "签署过期！", content, initiatorMail);
                }


                if (callBackEnum == 3) {
                    esignLogToSaveMap.put("esign_status", "6");
                    esignLogToSaveMap.put("execute_date", executeDate);
                    esignLogToSaveMap.put("name", "SA");

                }
            }


            daoDataMng.save("esign_instance", esignInstanceToSaveMap);
            daoDataMng.save("esign_log", esignLogToSaveMap);

            return;


        } catch (IOException e) {

            Log.error("", e);

        } catch (Exception e) {
            Log.error("", e);
        }

    }


    public static void processOrderSign(List<Map<String, Object>> singerList,DAODataMng daoDataMng,Map esignInstanceMap,String backProcessId,String projectId ) throws Exception {
        List<Map<String, String>> DAOLightpdfSignIntegrateList = DAOLightpdfSignIntegrate.listRule(projectId, "1");

        Map<String, String> DAOLightpdfSignIntegrateMap = DAOLightpdfSignIntegrateList.get(0);

        String CDTMSUrl = DAOLightpdfSignIntegrateMap.get(DAOLightpdfSignIntegrate.CDTMSUrl);
        String tableid = (String) esignInstanceMap.get("tableid");
        AttachDAO attachDAO = new AttachDAO(projectId);
        String fileSelected = (String) esignInstanceMap.get("selected_file");
        File[] attachFiles = null;
        String[] attachFileNames = null;
        if (StringUtils.isNotEmpty(fileSelected)) {
            String[] fileSelectedArr = fileSelected.split(",");
            attachFiles = new File[fileSelectedArr.length];
            attachFileNames = new String[fileSelectedArr.length];
            if (!ArrayUtils.isEmpty(fileSelectedArr)) {
                for (int i = 0; i < fileSelectedArr.length; ++i) {
                    String[] fileInfoArr = fileSelectedArr[i].split("\\|\\|");
                    if (fileInfoArr.length == 3) {
                        attachFiles[i] = attachDAO.getFile(fileInfoArr[0], fileInfoArr[2]);
                    } else {
                        attachFiles[i] = attachDAO.getFile(fileInfoArr[0], tableid);
                    }
                    //attachFiles[i] = attachDAO.getFile(fileSelectedArr[i].split("\\|\\|")[0], tableid);


                    attachFileNames[i] = fileSelectedArr[i].split("\\|\\|")[1];


                }
            }
        }
        Date signFlowExpireDate = (Date) esignInstanceMap.get("sign_expire_data");
        SimpleDateFormat DateFormatYMDHMS = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String signFlowExpireTime = DateFormatYMDHMS.format(signFlowExpireDate);

        String language = (String) esignInstanceMap.get("language");
        String fileName = (String) esignInstanceMap.get("file_name");
        String content2 = (String) esignInstanceMap.get("content");
        String initiator = (String) esignInstanceMap.get("initiator");
        // 会签模式下只处理第一个签署人，其他情况处理所有签署人
        for (Map signerInfoMap : singerList) {


            String signFlowId = (String) signerInfoMap.get("task_id");
            String content = null;
            String receiver = signerInfoMap.get("user_code").toString();
            String email = new String(Base64.getEncoder().encode(receiver.getBytes()));
            Map esignSignerMap = (Map) daoDataMng.listRecord("esign_signer", "obj.esign_instance_id=" + esignInstanceMap.get("id") + " and obj.user_code='" + receiver + "' and obj.status=-1", null, 1).get(0);
            esignSignerMap.put("status", "0");
            daoDataMng.saveRecord("esign_signer", esignSignerMap);
            //签字人
            String signerName="";
            if(!ObjectUtils.isEmpty(esignSignerMap.getOrDefault(CNT_Schema.name, ""))){
                signerName = esignSignerMap.getOrDefault(CNT_Schema.name, "").toString();
            }
            if (StringUtils.equals(language, "en")) {


                content = "Hello " + signerName +
                        "<br>You have a document \"" + fileName.replace(".pdf", "") + "\" need to be signed before " + signFlowExpireTime +
                        "<br>" +
                        "Signature CAPTCHA is " + esignSignerMap.get("verification_code")  +
                        "<br><br><a href=\"" + CDTMSUrl + "/lightpdfSign.checkIsLogin.do?system=" + projectId + "&task_id=" + backProcessId + "&email=" +
                        email + "\" title=\"go to sign\">Go to sign</a><br><br><br>" +
                        "Initiator: " + initiator +
                        "<br><span style=\"font-size='9px'\">This email is sent by the system, please do not reply</span>"
                + "<br><br><a href=\"https://cdtms.hengrui.com/webutil/js/pdfjs/web/viewer.show.do?file=/%E7%BA%BF%E4%B8%8A%E7%AD%BE%E5%90%8D%E5%8A%9F%E8%83%BD%E6%93%8D%E4%BD%9C%E6%8C%87%E5%8D%97_20231101.pdf&amp;unloadstr=\" target=\"_blank\" style=\"font-weight: bold;color: red;\">Signature operation guide</a>";

            } else {

                content = "您好，" + signerName + "<br> " +
                        "您有一份文件 《" + fileName.replace(".pdf", "") + "》 需要签署，请于" + signFlowExpireTime + "前完成。<br>" +
                        "签字验证码:" + esignSignerMap.get("verification_code") +
                        "<br><br><a href=\"" + CDTMSUrl + "/lightpdfSign.checkIsLogin.do?system=" + projectId + "&task_id=" + backProcessId + "&email=" +
                        email + "&language=" + language + "\" title=\"去签署\">去签署</a><br><br><br>" +
                        "签字发起人：" + initiator +
                        "<br><span style=\"font-size='9px'\">该邮件为系统发出，请勿回复</span>"
                + "<br><br><a href=\"https://cdtms.hengrui.com/webutil/js/pdfjs/web/viewer.show.do?file=/%E7%BA%BF%E4%B8%8A%E7%AD%BE%E5%90%8D%E5%8A%9F%E8%83%BD%E6%93%8D%E4%BD%9C%E6%8C%87%E5%8D%97_20231101.pdf&amp;unloadstr=\" target=\"_blank\" style=\"font-weight: bold;color: red;\">在线签署操作指南</a><br><br>";
            }


            if (content2.contains("<p>祝好</p>")) {
                // 如果包含，则在该内容前插入文本
                content = content2.replace("<p>祝好</p>", content + "<p>祝好</p>");
            } else {
                content = content + content2;
            }


            ThreadSendmail.addTask(projectId, "《" + fileName.replace(".pdf", "") + "》 need to be signed!", content, new String(Base64.getDecoder().decode(email.getBytes())),attachFiles, attachFileNames);


        }
    }


    public static String getFuncidForward(String projectId, String tableid, String recordid) throws Exception {
        DAODataMng dmdao = new DAODataMng(projectId);
        DAOPublicworkflow pwfdao = new DAOPublicworkflow(projectId);
        Map mapRuleWF = pwfdao.getWorkflowMap(tableid);
        Map mapR = pwfdao.getRule();
        String fid_fidstatus = (String)mapR.get("fid_fidstatus");
        String fid_rd_status1 = (String)mapR.get("fid_rd_status1");
        String fid_rd_status2 = (String)mapR.get("fid_rd_status2");
        String fid_rd_where = (String)mapR.get("fid_rd_where");
        String fidstatus = (String)mapRuleWF.get(fid_fidstatus);
        Map mapV = dmdao.getRecord(tableid, Long.valueOf(recordid));
        String status_curr = (String)mapV.get(fidstatus);
        List listWFDetail = pwfdao.getWorkflowDetail(tableid);

        for(int i = 0; i < listWFDetail.size(); ++i) {
            Map mapVFunction = (Map)listWFDetail.get(i);
            String rd_status1 = String.valueOf(mapVFunction.get(fid_rd_status1));
            String rd_status2 = String.valueOf(mapVFunction.get(fid_rd_status2));
            String rd_where = String.valueOf(mapVFunction.get(fid_rd_where));

            if (rd_status1.equals(status_curr) && Integer.parseInt(rd_status2) > Integer.parseInt(rd_status1)) {

                    if (rd_where != null && !rd_where.equals("")) {
                        rd_where = formatRDWhere(projectId, tableid, mapV, rd_where);
                        String whereTemp = "obj.id=" + recordid + " and (" + rd_where + ")";
                        if (dmdao.count(tableid, whereTemp) == 0) {
                           continue;
                        }
                    }
                        return String.valueOf(mapVFunction.get("id"));


            }
        }

        return null;
    }

    protected static String formatRDWhere(String projectId, String tableId, Map mapV, String where) throws Exception {
        Map mapVUser = new HashMap();
        if (SessUtil.getSessInfo() != null) {
            mapVUser = SessUtil.getSessInfo().getUser();
            if (mapVUser == null) mapVUser = new HashMap();
        }
        while (true) {
            int p = where.indexOf("$user{");
            if (p < 0) break;
            int p2 = where.indexOf("}", p);
            if (p2 < 0) break;
            String fid = where.substring(p + 6, p2);
            String func = "";
            if (fid.indexOf(".instr.") == 0) {
                func = "instr";
                fid = fid.substring(7);
            }
            String v = (mapVUser.get(fid) == null ? "" : String.valueOf(mapVUser.get(fid)));
            if (func.equals("instr")) {
                String[] vA = v.split("[,|\\s]+");
                String vInstr = "";
                for (int i = 0; i < vA.length; i++) {
                    if (i > 0) vInstr += ",";
                    vInstr += "'" + vA[i].trim() + "'";
                }
                v = vInstr;
            }
            where = where.substring(0, p) + v + where.substring(p2 + 1);
        }
        while (true) {
            int p = where.indexOf("{");
            if (p < 0) break;
            int p2 = where.indexOf("}", p);
            if (p2 < 0) break;
            String fid = where.substring(p + 1, p2);
            where = where.substring(0, p) + mapV.get(fid) + where.substring(p2 + 1);
        }
        return where;
    }


    //更改密码
    public void changepassword(HttpServletRequest request, HttpServletResponse response) {

        Map<String, String> currUserInfoMap = new HashMap<>();

        request.setAttribute("currUserInfoMap", currUserInfoMap);
        request.setAttribute("system_language", request.getParameter("language"));

        currUserInfoMap.put("email", request.getParameter("email"));
        currUserInfoMap.put("name", request.getParameter("name"));
        currUserInfoMap.put("language", request.getParameter("language"));
        currUserInfoMap.put("reg1", "2");
        String system = request.getParameter("system");
        request.setAttribute("system", system);

        this.forward(request, response, "register");

    }

    public void checkIsLogin(HttpServletRequest request, HttpServletResponse response) {
        try {
            Log.info("------进入了checkIsLogin的方法内部！------");

            String reqToken = request.getParameter("token");

            String system = request.getParameter("system");
            Log.info("------获取到的projectId是:"+system);
            request.setAttribute("system", system);

            DAODataMng daoDataMng = new DAODataMng(system);
            Map<String, String> currUserInfoMap = new HashMap<>();

            request.setAttribute("currUserInfoMap", currUserInfoMap);

            String taskId = request.getParameter("task_id");
            List<Map<String, String>> listR = DAOLightpdfSignIntegrate.listRule(system, "1");
            String esignUrl = listR.get(0).get(DAOLightpdfSignIntegrate.esignUrl);
            List<Map> esignInstanceList = daoDataMng.listRecord("esign_instance", "obj.sign_flow_id='" + taskId + "'", null, 1);

            if(StringUtils.isNotEmpty(reqToken)){
                String rspsystem = request.getParameter("rspsystem");
                String url = request.getParameter("url");


                String sessStr = URLUtil.getContentUTF8(url+"/lightpdfSign.checktoken.do?token="+reqToken, 3000);
                Gson gson = new Gson();
                Map<String, Object> su = gson.fromJson(sessStr, Map.class);

//            BeanSessUser su = new Gson().fromJson(sessStr, BeanSessUser.class);
                if(!String.valueOf(su.get("status")).equals("200")) {
                    response.getOutputStream().write("Token err".getBytes());

                    return;

                }

                currUserInfoMap = (Map) su.get("mapInfo");

                currUserInfoMap.put("url",url);
                currUserInfoMap.put("token",reqToken);
                request.setAttribute("currUserInfoMap", currUserInfoMap);

                String emaildecode = (String) currUserInfoMap.get("email");
                String system_language = (String) currUserInfoMap.get("system_language");
                request.setAttribute("system_language", system_language);
                //增加用户中英文名称的验证
               // List<Map> esignAccountList = daoDataMng.listRecord("esign_account", "obj.email='" + emaildecode + "'", null, 1);

                // 原条件
                String condition = "obj.email='" + emaildecode + "'";

                // 增加新条件后的拼接方式
                String newCondition = condition + " and obj.name_zh is not null  and obj.name_en is not null ";

                List<Map> esignAccountList = daoDataMng.listRecord("esign_account", newCondition, null, 1);
                if (CollectionUtils.isEmpty(esignInstanceList)) {

                    currUserInfoMap.put("status", "-1");
                    currUserInfoMap.put("msg", "Task Not Found");
                    this.forward(request, response, "Verify");
                    return;
                }
                Map curresignInstanceMap = esignInstanceList.get(0);
                List<Map> esignSignerList = daoDataMng.listRecord("esign_signer", "obj.esign_instance_id='" + curresignInstanceMap.get("id") + "' and  obj.user_code='" + emaildecode + "' and obj.status!='9'", null, 1);

                if (CollectionUtils.isEmpty(esignSignerList)) {
                    currUserInfoMap.put("status", "-1");
                    currUserInfoMap.put("msg", "You are not a signer of the current task.");
                    this.forward(request, response, "Verify");
                    return;
                }
                Map currSignerMap = esignSignerList.get(0);

                String redirectUrl = esignUrl + (StringUtils.equals((String) esignInstanceList.get(0).get("language"),"en")?"/en":"") +"?task_id=" + taskId + "&email=" + new String(Base64.getEncoder().encode( String.valueOf(currSignerMap.get("user_code")).getBytes("UTF-8"))) + "&verification_code=" + currSignerMap.get("verification_code");
                String currloginid = SessUtil.getSessInfo().getUserloginid();
                if (CollectionUtils.isEmpty(esignAccountList)) {
                    Log.info("------登录校验没查到该用户信息，所以跳转到签字用户注册页面------");
                    currUserInfoMap.put("isNew", "1");
                    String code = String.valueOf(new Date().getTime());
                    code = code.substring(code.length() - 6);
                    mapCodeCache.put(emaildecode, code);
                    mapCodeTimeCache.put(emaildecode, String.valueOf(new Date().getTime()));
                    currUserInfoMap.put("verificationCode", code);
                    currUserInfoMap.put("reg1", "1");

                    this.forward(request, response, "register");
//                this.forwardByUri(request,response,"register");
                    return;

                }

                //更新签署用户的英文名字
                updateSignerENName(esignUrl,taskId,new String(Base64.getEncoder().encode( emaildecode.getBytes("UTF-8"))),(String) esignAccountList.get(0).get("name_en"));


                if (StringUtils.equals(currloginid, emaildecode)) {


                    Log.info("------检测到您已登录系统，即将自动跳转至签字页------");
                    currUserInfoMap.put("msg", "检测到您已登录系统，即将自动跳转至签字页");

                    this.forward(request, response, "Verify");
                    return;

                }
                currUserInfoMap.put("redirectUrl", redirectUrl);




                this.forward(request, response, "verify");


                return;
            }


//            request.setAttribute("system_language", getlocStr(request));

            String email = request.getParameter("email");

            String emaildecode = new String(Base64.getDecoder().decode(email));






            if (CollectionUtils.isEmpty(esignInstanceList)) {

                currUserInfoMap.put("status", "-1");
                currUserInfoMap.put("msg", "Task Not Found");
                this.forward(request, response, "Verify");
                return;
            }
            request.setAttribute("system_language", esignInstanceList.get(0).get("language"));

            currUserInfoMap.put("title", (String) esignInstanceList.get(0).get("subject"));
            currUserInfoMap.put("language", (String) esignInstanceList.get(0).get("language"));
            currUserInfoMap.put("id", String.valueOf(esignInstanceList.get(0).get("id")));
            currUserInfoMap.put("taskId", taskId);
            currUserInfoMap.put("email", emaildecode);
            currUserInfoMap.put("system", system);
            Map curresignInstanceMap = esignInstanceList.get(0);
//            SimpleDateFormat sdf = new SimpleDateFormat("MMMM d, yyyy", Locale.US);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

            String esignInstanceStatus = (String) curresignInstanceMap.get("status");
            currUserInfoMap.put("status", esignInstanceStatus);
            Date esignInitiatDate = (Date) curresignInstanceMap.get("esign_begin_date");
            String esignInitiatDateStr = sdf.format(esignInitiatDate);
            currUserInfoMap.put("notes", "Created by " + curresignInstanceMap.get("initiator") + " on " + esignInitiatDateStr);
            currUserInfoMap.put("initiator", (String) curresignInstanceMap.get("initiator"));
            currUserInfoMap.put("InitiatDate", esignInitiatDateStr);

            List<Map> esignSignerList = daoDataMng.listRecord("esign_signer", "obj.esign_instance_id='" + curresignInstanceMap.get("id") + "' and  obj.user_code='" + emaildecode + "' and obj.status!='9'", null, 1);


            if (CollectionUtils.isEmpty(esignSignerList)) {
                currUserInfoMap.put("status", "-1");
                currUserInfoMap.put("msg", "You are not a signer of the current task.");
                this.forward(request, response, "Verify");
                return;
            }
            Map currSignerMap = esignSignerList.get(0);


            String esignEndDate = ObjectUtils.isNotEmpty(curresignInstanceMap.get("esign_end_date")) ? sdf.format((Date) curresignInstanceMap.get("esign_end_date")) : "";
            String esignExpireData = ObjectUtils.isNotEmpty(curresignInstanceMap.get("sign_expire_data")) ? sdf.format((Date) curresignInstanceMap.get("sign_expire_data")) : "";
            String esignExecuteDate = ObjectUtils.isNotEmpty(currSignerMap.get("execute_date")) ? sdf.format((Date) currSignerMap.get("execute_date")) : "";
            currUserInfoMap.put("name", (String) currSignerMap.get("name"));

            String signMsg = null;
            if (StringUtils.isNotEmpty(esignExecuteDate)) {
                signMsg = ",Your signing time is " + esignExecuteDate;
            } else {
                signMsg = ",You have not signed.";
            }

            String msg = null;

            if (StringUtils.equals(esignInstanceStatus, "2")) {
                msg = "The task has been signed completed on " + esignEndDate + signMsg;
                currUserInfoMap.put("msg", msg);

                this.forward(request, response, "Verify");
                return;

            }

            if (StringUtils.equals(esignInstanceStatus, "3")) {
                msg = "The task expired on " + esignExpireData + ", and the signing has been terminated.";
                currUserInfoMap.put("msg", msg);

                this.forward(request, response, "Verify");
                return;
            }


            String cancelor = (String) curresignInstanceMap.get("cancelor");
            String cancelCause = (String) curresignInstanceMap.get("cancel_cause");


            if (StringUtils.equals(esignInstanceStatus, "5")) {
                msg = "The task was initiated to terminate signing by " + cancelor + " due to " + cancelCause + " on " + esignEndDate + ",and the signing has been terminated." + signMsg;
                currUserInfoMap.put("msg", msg);
                this.forward(request, response, "Verify");
                return;
            }


            String esignSigerStatus = (String) currSignerMap.get("status");
            if (StringUtils.equals(esignSigerStatus, "2")) {
                //msg = "The task you have signed has been completed on " + esignExecuteDate;
                msg = "The doucument has been signed,No action needed";
                currUserInfoMap.put("msg", msg);

                this.forward(request, response, "Verify");
                return;
            }


//            List<SessInfo> sessInfoList = SessUtil.listLoginedSessInfo(system);
            String redirectUrl = esignUrl + (StringUtils.equals((String) esignInstanceList.get(0).get("language"),"en")?"/en":"") +"?task_id=" + taskId + "&email=" + new String(Base64.getEncoder().encode( String.valueOf(currSignerMap.get("user_code")).getBytes("UTF-8"))) + "&verification_code=" + currSignerMap.get("verification_code");
            String currloginid = SessUtil.getSessInfo().getUserloginid();
            //增加用户中英文名称的验证
            // List<Map> esignAccountList = daoDataMng.listRecord("esign_account", "obj.email='" + emaildecode + "'", null, 1);

            // 原条件
            String condition = "obj.email='" + emaildecode + "'";

            // 增加新条件后的拼接方式
            String newCondition = condition + " and obj.name_zh is not null  and obj.name_en is not null ";

            List<Map> esignAccountList = daoDataMng.listRecord("esign_account", newCondition, null, 1);

            if (CollectionUtils.isEmpty(esignAccountList)) {
                Log.info("------登录校验没查到该用户信息并且没有请求token，所以跳转到签字用户注册页面------");
                currUserInfoMap.put("isNew", "1");

                String code = String.valueOf(new Date().getTime());
                code = code.substring(code.length() - 6);
                mapCodeCache.put(emaildecode, code);
                mapCodeTimeCache.put(emaildecode, String.valueOf(new Date().getTime()));
                currUserInfoMap.put("verificationCode", code);
                currUserInfoMap.put("reg1", "1");
                currUserInfoMap.put("redirectUrl", redirectUrl);
                this.forward(request, response, "register");
//                this.forwardByUri(request,response,"register");
                return;

            }
            //更新签署用户的英文名字
            updateSignerENName(esignUrl,taskId,new String(Base64.getEncoder().encode( emaildecode.getBytes("UTF-8"))),(String) esignAccountList.get(0).get("name_en"));
           if(StringUtils.equals((String) esignInstanceList.get(0).get("language"),"en")){
               currUserInfoMap.put("name", (String) esignAccountList.get(0).get("name_en"));
           }else{
               currUserInfoMap.put("name", (String) esignAccountList.get(0).get("name_zh"));
           }


            if (StringUtils.equals(currloginid, emaildecode)) {



                currUserInfoMap.put("msg", "检测到您已登录系统，即将自动跳转至签字页");

                currUserInfoMap.put("redirectUrl", redirectUrl);
                this.forward(request, response, "Verify");
                return;

            }


            String checkAccountUrl = listR.get(0).get(DAOLightpdfSignIntegrate.checkAccountUrl);
            String checkProjectid = listR.get(0).get(DAOLightpdfSignIntegrate.checkProjectid);
            String clinicalinteaddr_pro = listR.get(0).get(DAOLightpdfSignIntegrate.clinicalinteaddr_pro);


            if (StringUtils.isNotEmpty(checkAccountUrl)) {

                Log.info("------一体化地址："+checkAccountUrl+"跳转链接："+redirectUrl);
                currUserInfoMap.put("redirectUrl",redirectUrl);
                currUserInfoMap.put("system_language", (String) esignInstanceList.get(0).get("language"));

                String token = UtilTokenSession.initToken(system, "sa", UUIDUtil.get(), currUserInfoMap);

//                this.forward(request,response,);

                this.redirectByUrl(request,response,checkAccountUrl+"/lightpdfSign.checkIsLogin.do?token="+token+"&url="+clinicalinteaddr_pro+"&rspsystem="+system+"&system="+checkProjectid);
                return;
            }
//            checkAccountUrl






//            String remoteAddr = request.getRemoteAddr();
//            boolean sessExists=false;
//            boolean sessExists = sessInfoList.stream()
//                    .anyMatch(sessInfo ->
//                            StringUtils.equals(sessInfo.getUserloginid(), emaildecode) &&
//                                    StringUtils.equals(sessInfo.getIp(), remoteAddr)
//                    );
//


//            if (sessExists) {
//                currUserInfoMap.put("verification_code", (String) esignSignerList.get(0).get("verification_code"));
//                this.forward(request, response, "Verify");
//                return;
//            }


            this.forward(request, response, "Verify");
            return;

        } catch (IOException ex) {
            throw new RuntimeException(ex);
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }


    }



    public void updateSignerENName(String esignUrl,String taskId,String userEmailEncode,String enName){
        //更新签署人英文名
        String Msg= LightpdfSignIntegrateUtil.httpGet(esignUrl + "/api/tasks/" + taskId+"/users?per_page=200&page=1");
        JSONObject orderSignMsgMap = JSONObject.parseObject(Msg);
        String data=orderSignMsgMap.get("data").toString();
        JSONObject dataObj = JSONObject.parseObject(data);
        String items = dataObj.get("items").toString();
        JSONArray array = JSONArray.parseArray(items);
        String targetEmail=userEmailEncode;
        if(array.size()>0){
            // 遍历数组查找匹配的email
            String matchedId = null;
            int index =0;
            for (int i = 0; i < array.size(); i++) {
                JSONObject item = array.getJSONObject(i);
                if (item.containsKey("email") && targetEmail.equals(item.getString("email"))) {
                    // 找到匹配的email，获取id
                    matchedId = item.getString("id");
                    index=i;
                    Log.info("找到匹配的email，对应的id为: " + matchedId);
                    break;
                }
            }

            if (matchedId == null) {
                Log.info("未找到匹配的email: " + targetEmail);
            }
            if(matchedId!=null){
                JSONObject item = array.getJSONObject(index);
                item.put("name_en",enName);
                item.put("task_id",taskId);
                item.remove("created_at");
                item.remove("email");
                item.remove("status");
                item.remove("verification_code");
                // 处理items数组，转换键名格式
                if (item.containsKey("items")) {
                    JSONArray itemsArray = item.getJSONArray("items");
                    JSONArray newItemsArray = new JSONArray();

                    for (int i = 0; i < itemsArray.size(); i++) {
                        JSONObject itemObj = itemsArray.getJSONObject(i);
                        JSONObject newItemObj = new JSONObject();

                        // 转换键名并将值转为字符串
                        if (itemObj.containsKey("Page"))
                            newItemObj.put("page", itemObj.getIntValue("Page"));
                        if (itemObj.containsKey("PositionX"))
                            newItemObj.put("position_x", itemObj.getString("PositionX"));
                        if (itemObj.containsKey("PositionY"))
                            newItemObj.put("position_y", itemObj.getString("PositionY"));
                        if (itemObj.containsKey("DatePositionX"))
                            newItemObj.put("date_position_x", itemObj.getString("DatePositionX"));
                        if (itemObj.containsKey("DatePositionY"))
                            newItemObj.put("date_position_y", itemObj.getString("DatePositionY"));

                        newItemsArray.add(newItemObj);
                    }

                    // 替换原有的items数组
                    item.put("items", newItemsArray);
                }
                String result= LightpdfSignIntegrateUtil.httpPut(esignUrl + "/api/tasks/" + taskId+"/users/"+matchedId, item.toString());
                Log.info("更新签字人英文名返回的信息:"+result+"传递的参数为:"+item.toString());
                String resultB= LightpdfSignIntegrateUtil.httpPut(esignUrl + "/api/tasks/" + taskId,"{\"type\":1,\"remark\": \"changeEnName\"}");
                Log.info("更新英文名下发后的信息:"+resultB+"传递的参数为:"+item.toString());
            }
        }
    }


    public void checktoken(HttpServletRequest request, HttpServletResponse response) {
        try {


            String token = request.getParameter("token");

            BeanSessUser su = null;
            if (token != null && !token.equals("")) {
                su = (BeanSessUser) TimerCacheUtil.get(token);
            }
            String ret = "";
            if (su == null) {
                ret = "{\"status\":\"404\",\"note\":\"rtoken invalid.\"}";
            } else {
                ret = new Gson().toJson(su);
            }
            response.getOutputStream().write(ret.getBytes("UTF8"));
            response.getOutputStream().close();


        } catch (Exception e) {
            Log.error("", e);
        }

    }
    public void createCheck(HttpServletRequest request, HttpServletResponse response) {
        try {

            // 读取并解析请求体中的JSON数据
            String requestBody = request.getReader().lines().collect(Collectors.joining(System.lineSeparator()));
            // 创建支持Map类型解析的Gson实例
            GsonBuilder gsonBuilder = new GsonBuilder();
            gsonBuilder.registerTypeAdapter(new TypeToken<Map<String, Object>>() {
            }.getType(), new MapDeserializerDoubleAsIntFix());
            Gson gson = gsonBuilder.create();
            Map<String, String> requestDataMap = gson.fromJson(requestBody, new TypeToken<Map<String, String>>() {
            }.getType());
            String recipients = requestDataMap.get("recipients");  // 收件人列表（分号分隔）
            String subject = requestDataMap.get("subject"); // 邮件主题
            String signFlowExpireTimeStr = requestDataMap.get("signFlowExpireTime");  // 签名流程过期时间
            String signpage = requestDataMap.get("signpage");  // 签名页内容
            String signPageFile = requestDataMap.get("signPageFile");// 签名页文件
            String attachments = requestDataMap.get("attachments");// 签名附件
            String language = requestDataMap.get("language");   // 语言设置
            String type = requestDataMap.get("type");                 // 签名类型
            String signType = requestDataMap.get("signType");                 // 会签
//            String studyid = requestDataMap.get("studyid");
//            Long studyid =null;
            // 处理研究ID参数（兼容空值）
            Long studyid = null;


            if (ObjectUtils.isNotEmpty(requestDataMap.get("studyid"))) {
                studyid = Long.valueOf(requestDataMap.get("studyid")); // 转换为Long类型
            }

            String body = requestDataMap.get("body"); // 邮件正文内容

            // 构建收件人列表
            List<Map<String, String>> recipientList = new ArrayList<>();
            String[] recipientsArr = recipients.split(";"); // 按分号分割收件人
            for (String recipient : recipientsArr) {
                // 使用正则拆分姓名和邮箱（格式：张三 <<EMAIL>>）
                String[] recipientArr = recipient.trim().split("[<>]");


                String name = recipientArr[0].trim();// 收件人姓名
                // 处理无邮箱地址的情况（默认使用姓名作为邮箱）
                String email = recipientArr.length > 1 ? recipientArr[1].trim() : recipientArr[0].trim();
                // 构建收件人Map
                Map<String, String> recipienMap = new HashMap<>();
                recipienMap.put("name", name);
                recipienMap.put("value", email);
                recipientList.add(recipienMap);
            }
            // 初始化文件操作相关组件
            String projectId = SessUtil.getSessInfo().getProjectid(); // 获取当前项目ID
            DAOFileup fdao = new DAOFileup();
            AttachDAO attachDAO = new AttachDAO(projectId);
            // 处理签名页文件
            File signpageFileLocal;
            String signpageFileName;
            if (StringUtils.isEmpty(signPageFile)) {
                // 解析并转换本地签名页文件为PDF
                List signpageList = fdao.parseToFile(signpage);
                List<String> signpageFileNameList = fdao.parseToFileName(signpage);
                File signpageFile = (File) signpageList.get(0);
                signpageFileName = signpageFileNameList.get(0);

                int fileNameLastDotIndex = signpageFileName.lastIndexOf(".");
                signpageFileName = signpageFileName.substring(0, fileNameLastDotIndex) + ".pdf";

                boolean signpageFileIspdf = signpageFile.getName().toLowerCase().endsWith(".pdf");
                String signFilePathFullOutURI = null;
                if (!signpageFileIspdf) {
                    String signFilePathFullURI = signpageFile.getAbsolutePath();
                    int lastDotIndex = signFilePathFullURI.lastIndexOf(".");
                    signFilePathFullOutURI = signFilePathFullURI.substring(0, lastDotIndex) + ".pdf";
                    UtilAsposeword.word2pdf(signFilePathFullURI, signFilePathFullOutURI);

                }

                signpageFileLocal = new File(signpageFileIspdf ? signpageFile.getAbsolutePath() : signFilePathFullOutURI);

            } else {
                signpageFileName = signPageFile.split("\\|")[0].split("\\*")[0];
                String signedFileId = signPageFile.split("\\|")[0].split("\\*")[1];
                signpageFileLocal = attachDAO.getFile(signedFileId, "esign_file");
            }

            List<Map<String, String>> listR = DAOLightpdfSignIntegrate.listRule(projectId, "1");
            String esignUrl = listR.get(0).get(DAOLightpdfSignIntegrate.esignUrl);
            String md5 = generateMD5(signpageFileLocal.getPath());

            String uploadFileMsgJson = LightpdfSignIntegrateUtil.uploadFile(esignUrl + "/api/files/upload", signpageFileLocal.getPath(), signpageFileName);


            Map uploadFileMsgMap = gson.fromJson(uploadFileMsgJson, new TypeToken<Map<String, Object>>() {
            }.getType());
            // 验证文件上传结果
            if (!StringUtils.equals(String.valueOf(uploadFileMsgMap.get("status")), "200")) {
                response.getOutputStream().write(String.valueOf(uploadFileMsgMap.get("message")).getBytes(StandardCharsets.UTF_8));
                response.getOutputStream().close();
                return;
            }
            // 构建电子签名实例参数
            Map<String, Object> esignInstanceMap = new HashMap();
            esignInstanceMap.put("subject", subject);// 签署主题
            esignInstanceMap.put("file_code", md5);// 文件MD5校验码
            // 计算签名流程过期时间（当前时间 + N天）
            esignInstanceMap.put("signFlowExpireTime", new Date(new Date().getTime() + (Long.valueOf(signFlowExpireTimeStr) * 24 * 60 * 60 * 1000)));
            // 解析文件上传返回数据
            Map uploadFileDataMap = (Map) uploadFileMsgMap.get("data");
            List uploadFileDataList = (List) uploadFileDataMap.get("items");
            Map uploadFileData0Map = (Map) uploadFileDataList.get(0);
            // 获取文件唯一标识
            String eSignfileKey = (String) uploadFileData0Map.get("file_id");

            // 获取电子签名配置规则
            List<Map<String, String>> DAOLightpdfSignIntegrateList = DAOLightpdfSignIntegrate.listRule(projectId, "1");
            Map<String, String> DAOLightpdfSignIntegrateMap = DAOLightpdfSignIntegrateList.get(0);
            // 生成签署人信息列表
            ArrayList<Map> signerInfosList = getSignerInfosList(signpageFileLocal, recipientList);

            Map<String, String> eSignEngineMap =new HashMap<>();
            Log.info("----------------------------------------获取到的会签类型的值："+signType);
            eSignEngineMap.put("signType",signType);
            Map esignInstanceToSaveMap = new HashMap();
            // 初始化DAO操作
            DAODataMng daoDataMng = new DAODataMng(projectId);
            esignInstanceToSaveMap.put("study_id", studyid);// 关联研究ID
            esignInstanceToSaveMap.put("type", type); // 签署类型

            daoDataMng.save("esign_instance", esignInstanceToSaveMap);
            Long esignInstanceId = (Long) esignInstanceToSaveMap.get("id");
            //构造文件存储链接 https://meduap-tst.hengrui.com:8085/home.jsp?projectid=cdtmsen_val&gotopage=/uapvue/index.html#/normalform?tableid=esign_instance&recordid=4045438983
            String urlSufix = String.format("/uapvue/index.html#/normalform?tableid=esign_instance&recordid=%d", esignInstanceId);
            urlSufix=URLEncoder.encode(urlSufix);
            esignInstanceMap.put("urlSufix",urlSufix);
            // 调用电子签名注册接口
            String CNSMsg = LightpdfSignIntegrateUtil.eSignRegister(projectId, eSignfileKey, esignInstanceMap, signerInfosList, DAOLightpdfSignIntegrateMap,eSignEngineMap,language,studyid);

            // 解析电子签名注册结果
            Map CNSMsgMap = gson.fromJson(CNSMsg, new TypeToken<Map<String, Object>>() {
            }.getType());

            if (!StringUtils.equals(String.valueOf(CNSMsgMap.get("status")), "200")) {
                String message = (String) CNSMsgMap.get("message");
                response.getOutputStream().write(message.getBytes(StandardCharsets.UTF_8));
                response.getOutputStream().close();
                return;

            }

            // 提取任务ID
            Map GUUDataMap = (Map) CNSMsgMap.get("data");
            String signFlowId = (String) GUUDataMap.get("task_id");

            // 构建要保存的签名实例数据

            esignInstanceToSaveMap.put("active", "1"); // 激活状态
            esignInstanceToSaveMap.put("sign_flow_id", signFlowId); // 签名流程ID
            esignInstanceToSaveMap.put("status", "1");  // 初始状态
            esignInstanceToSaveMap.put("esign_msg", CNSMsg);// 原始响应数据
            esignInstanceToSaveMap.put("language", language);// 语言设置

            esignInstanceToSaveMap.put("content", body); // 邮件正文内容



            Date signFlowExpireTime = (Date) esignInstanceMap.get("signFlowExpireTime");
//            SimpleDateFormat DateFormatYMDHMS = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//            Date signFlowExpireDate = DateFormatYMDHMS.parse(signFlowExpireTime);
            // 设置实例创建者信息
            Map currentUserMap = SessUtil.getSessInfo().getUser();
            esignInstanceToSaveMap.put("initiator", currentUserMap.get("username")); // 发起人

            esignInstanceToSaveMap.put("remark", esignInstanceMap.get("remark"));
            // 设置文件元数据
            esignInstanceToSaveMap.put("subject", signpageFileName);// 文件主题
            esignInstanceToSaveMap.put("sign_expire_data", signFlowExpireTime); // 签署过期时间
            esignInstanceToSaveMap.put("type", type); // 签署类型
            esignInstanceToSaveMap.put("esign_begin_date", new Date());// 开始时间
            esignInstanceToSaveMap.put("study_id", studyid);// 关联研究ID
            esignInstanceToSaveMap.put("file_name", signpageFileName);// 文件名
            esignInstanceToSaveMap.put("esign_engine_id",eSignEngineMap.get("id"));
            //邮件附件信息
            if (StringUtils.isNotEmpty(attachments)) {
                esignInstanceToSaveMap.put("selected_file",attachments);
            }
            // 保存签名实例到数据库
            daoDataMng.save("esign_instance", esignInstanceToSaveMap);
            // 构建签署人保存列表
            ArrayList<Map> signUrlInfosToSaveList = new ArrayList<>();
            for (Map signUrlInfosMap : signerInfosList) {
                Map signUrlInfosToSaveMap = new HashMap<>();
                signUrlInfosToSaveMap.put("name",  (String) signUrlInfosMap.get("name_zh"));// 签署人姓名
                // 解码邮箱地址
                String email = (String) signUrlInfosMap.get("email");
                signUrlInfosToSaveMap.put("user_code", new String(Base64.getDecoder().decode(email.getBytes())));
                signUrlInfosToSaveMap.put("verification_code", signUrlInfosMap.get("verification_code"));// 验证码
                signUrlInfosToSaveMap.put("esign_url", signUrlInfosMap.get("signUrlShort")); // 签署短链接
                signUrlInfosToSaveMap.put("signer_id", String.valueOf(signUrlInfosMap.get("signer_id")));// 签署人ID
                signUrlInfosToSaveMap.put("esign_instance_id", esignInstanceToSaveMap.get("id")); // 关联实例ID
                signUrlInfosToSaveMap.put("status", "5");// 签署状态
                signUrlInfosToSaveList.add(signUrlInfosToSaveMap);
//				daoDataMng.save("esign_signer",signUrlInfosToSaveMap );
//				daoDataMng.saveBatch("esign_signer", (List) signUrlInfosToSaveList, 2L,null);


            }
            // 批量保存签署人信息
            Long userid = Long.valueOf(SessUtil.getSessInfo().getUserid());

            daoDataMng.saveBatch("esign_signer", signUrlInfosToSaveList, userid, null);

            // 保存文件关联信息
            Map<Object, Object> esignFileMap = new HashMap<>();


            esignFileMap.put("esign_file_key", eSignfileKey); // 文件唯一标识
            String fileLocalUuid = attachDAO.saveFile(signpageFileLocal, "esign_file");// 保存本地文件
            String strAttachToSave = "" + signpageFileName + "*" + fileLocalUuid + "|";// 构建附件存储格式
            esignFileMap.put("esign_file_key", eSignfileKey);
            esignFileMap.put("file", strAttachToSave);// 文件存储路径
            esignFileMap.put("file_md5", md5); // 文件MD5


            esignFileMap.put("esign_instance_id", esignInstanceToSaveMap.get("id"));

            daoDataMng.save("esign_file", esignFileMap);

            // 记录操作日志
            Map esignLogToSaveMap = new HashMap<>();
            esignLogToSaveMap.put("esign_instance_id", esignInstanceToSaveMap.get("id"));
            esignLogToSaveMap.put("esign_status", "0");
//            esignLogToSaveMap.put("msg", ReceiveMsg);
//            esignLogToSaveMap.put("esign_desc", callBackDesc);
            esignLogToSaveMap.put("execute_date", new Date());
            esignLogToSaveMap.put("name", currentUserMap.get("username"));


            daoDataMng.save("esign_log", esignLogToSaveMap);

            // 构建响应数据
            Map<Object, Object> responseData = new HashMap<>();
            String redirectUrl = esignUrl + "/set-sign?file_id=" + eSignfileKey + "&task_id=" + signFlowId;// 签署页面URL
            responseData.put("setUrl", redirectUrl);
            responseData.put("redirectUrl", "/tableapp.edit.do?tableid=esign_instance&id=" + esignInstanceToSaveMap.get("id"));// 管理后台URL

            // 返回最终响应
            ResponseUtils.response(response, HttpStatus.OK, responseData);


            return;


        } catch (Exception e) {
            Log.error("err", e);
        }


    }

    public void eSign(HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {


            String projectId = SessUtil.getSessInfo().getProjectid();
            DAODataMng daoDataMng = new DAODataMng(projectId);

            String tableid = request.getParameter("tableid");
            Long recordid = Long.valueOf(request.getParameter("recordid"));
            String anewSign = request.getParameter("anewSign");
            String funcid = request.getParameter("funcid");
            String anewInstanceId = request.getParameter("currid");
            request.setAttribute("isSign", "1");

            if (StringUtils.isNotEmpty(anewSign) && StringUtils.isNotEmpty(anewInstanceId)) {
                funcid = String.valueOf(daoDataMng.getRecord("esign_instance", Long.valueOf(anewInstanceId)).get("esign_engine_id"));
                Map anewInstanceToSaveMap = new HashMap<>();
                anewInstanceToSaveMap.put("id", Long.valueOf(anewInstanceId));
                anewInstanceToSaveMap.put("active", "0");
                daoDataMng.save("esign_instance", anewInstanceToSaveMap);
            } else if (StringUtils.isEmpty(funcid)) {
                List<Map> engineList = daoDataMng.listRecord("esign_engine", "obj.tableid='" + tableid + "'", null, 100);

                for (Map engineMap : engineList) {
                    String showWhere = (String) engineMap.get("where");
                    Long engineId = (Long) engineMap.get("id");

                    int count = daoDataMng.count(tableid, "obj.id=" + recordid + " and (" + showWhere + ")");

                    List esignInstanceList = daoDataMng.listRecord("esign_instance", "obj.active = 1 and obj.tableid='" + tableid + "' and obj.recordid='" + recordid + "' and obj.sign_flow_id is not null and obj.esign_engine_id=" + engineMap.get("id"), null, 1);

                    if (CollectionUtils.isEmpty(esignInstanceList) && (org.apache.commons.lang.StringUtils.isEmpty(showWhere) || count > 0)) {
                        funcid = String.valueOf(engineId);
                    }

                }


            }


            request.setAttribute("signfuncid", funcid);


            Map<String, String> eSignEngineMap = daoDataMng.getRecord("esign_engine", Long.valueOf(funcid));
            String sginRole = (String) eSignEngineMap.get("sgin_role");
            String toSignFieldid = (String) eSignEngineMap.get("to_sign_field");
            String signPageFiledid = (String) eSignEngineMap.get("sign_page_filed");

            DtrefDAO dtrefDAO = new DtrefDAO(projectId);

            String eSignDataRefField = dtrefDAO.getRefField("xsht", tableid);

            Map eSignDataMap = daoDataMng.getRecord(tableid, recordid);

            Long studyId = (Long) eSignDataMap.get(eSignDataRefField);

            String toSignFiles = (String) eSignDataMap.get(toSignFieldid);


            if (StringUtils.isEmpty(toSignFiles)) {

                response.getOutputStream().write("请上传待签署文件!".getBytes(StandardCharsets.UTF_8));
                response.getOutputStream().close();
                return;
            }


            ArrayList<Object> eSignFileList = new ArrayList<>();

            for (String toSignFile : toSignFiles.split("\\|")) {
                Map eSignFileMap = new HashMap<>();
                String[] toSignFileArr = toSignFile.split("\\*");
                eSignFileMap.put("file_uuid", toSignFileArr[1]);
                eSignFileMap.put("filename", toSignFileArr[0]);
                eSignFileList.add(eSignFileMap);
            }



            //增加药物安全数据一致性检查(yzxhc) 附件email 转pdf回填功能
            if ("yzxhc".equals(tableid)) {
                Log.info("增加药物安全数据一致性检查(yzxhc) 附件email 转pdf回填功能");
                String emailAttach = (String) eSignDataMap.get("email");
                if (StringUtils.isNotEmpty(emailAttach)) {
                    AttachDAO attachDAO = new AttachDAO(projectId);
                    // 解析附件信息（假设存储格式为 filename*uuid|...）
                    String[] attachInfo = emailAttach.split("\\|")[0].split("\\*");
                    if (attachInfo.length > 1) {
                        File emailFile = attachDAO.getFile(attachInfo[1], tableid);
                        String fileName = attachInfo[0];  // 获取显示名称
                        Log.info("---------------------获取到的文件名是：" + fileName);
                        // 检查文件扩展名
                        if (emailFile.getName().toLowerCase().endsWith(".xlsx")) {

                            String uploadResult = LightpdfSignIntegrateUtil.uploadFile(sasOnlieApiPrefix+"transferXlsxToPdf", emailFile.getAbsolutePath().toString(), fileName);
                            //判断接口是否成功
                            Gson gson = new Gson();
                            Map uploadFileMsgMap = gson.fromJson(uploadResult, new TypeToken<Map<String, Object>>() {
                            }.getType());
                            Log.info("请求了pdf转换接口，返回结果为：" + uploadFileMsgMap.toString());
                            if (StringUtils.equals(String.valueOf(uploadFileMsgMap.get("code")), "200.0")) {
                                // 下载文件流，保存到本地
                                String folderPath = WebPath.getRootPath() + DAOFileup.tempfolder + "/eSign";
                                File folder = new File(folderPath);
                                if (!folder.exists()) {
                                    folder.mkdir();
                                }
                                String pdfName = fileName.substring(0, fileName.lastIndexOf(".")) + ".pdf";
                                String endcodePdfName = URLEncoder.encode(pdfName, StandardCharsets.UTF_8.toString());
                                String downloadUrl = sasOnlieApiPrefix+"files/" + endcodePdfName;
                                LightpdfSignIntegrateUtil.downloadByNIO(downloadUrl, folderPath, pdfName);

                                // 获取原email附件信息
                                String originalEmailAttach = (String) eSignDataMap.get("email");
                                StringBuilder newEmailAttach = new StringBuilder(originalEmailAttach);

                                // 保存pdf并追加到附件字段
                                File pdfFile = new File(folderPath + "/" + pdfName);
                                String fileUuid = attachDAO.saveFile(pdfFile, tableid);

                                // 构造新的附件信息（保留原附件，追加新pdf）
                                String lastAttachment = "";
                                if (StringUtils.isNotEmpty(originalEmailAttach)) {
                                    String[] attachments = originalEmailAttach.split("\\|");
                                    lastAttachment = attachments[attachments.length - 1]; // 获取最后一个附件
                                }

                                // 新增后缀名检查 ↓
                                if (!lastAttachment.toLowerCase().endsWith(".pdf")) {
                                    newEmailAttach.append(pdfName).append("*").append(fileUuid).append("|");

                                    // 更新记录中的email字段
                                    DAODataMng dmdao = new DAODataMng(projectId);
                                    Map record = dmdao.getRecord(tableid, Long.valueOf(recordid));
                                    record.put("email", newEmailAttach.toString());
                                    dmdao.save(tableid, record);
                                    Log.info("PDF附件已追加，当前email字段值：" + record.get("email"));
                                } else {
                                    Log.info("检测到已有PDF附件，跳过追加操作");
                                }
                            }
                        }
                    }
                }
            }


//            Map signPageFileMap = new HashMap<>();
                ArrayList<Object> signPageFileList = new ArrayList<>();
                //签字页处理
                if (StringUtils.isNotEmpty(signPageFiledid)) {
                    String signPageFiles = (String) eSignDataMap.get(signPageFiledid);
                    if (StringUtils.isEmpty(signPageFiles)) {
                        response.getOutputStream().write("请上传签字页!".getBytes(StandardCharsets.UTF_8));
                        response.getOutputStream().close();
                        return;

                    }


                    for (String signPageFile : signPageFiles.split("\\|")) {
                        Map signPageFileMap = new HashMap<>();
                        String[] toSignFileArr = signPageFile.split("\\*");
                        signPageFileMap.put("file_uuid", toSignFileArr[1]);
                        signPageFileMap.put("filename", toSignFileArr[0]);
                        signPageFileList.add(signPageFileMap);
                    }


//                String[] signPageFileArr = signPageFile.split("\\|")[0].split("\\*");
//                signPageFileMap.put("filename", signPageFileArr[0]);
//
//                signPageFileMap.put("file_uuid", signPageFileArr[1]);


                }

                request.setAttribute("eSignFileList", eSignFileList);
                request.setAttribute("signPageFileList", signPageFileList);
                Map<Object, Object> esignInstanceMap = new HashMap<>();
                esignInstanceMap.put("edition_name", eSignDataMap.get(eSignEngineMap.get("edition_filed")));
                Date editionDate = (Date) eSignDataMap.get(eSignEngineMap.get("edition_date_filed"));

                esignInstanceMap.put("edition_date", editionDate);

                request.setAttribute("esignInstanceMap", esignInstanceMap);
                String stuydUser = LightpdfSignIntegrateUtil.getStudyUser(studyId, sginRole, projectId);
                request.setAttribute("stuydUser", stuydUser);


                String mailTemplateType = (String) eSignEngineMap.get("mail_template_type");
                if (!StringUtils.isEmpty(mailTemplateType)) {
                    this.forwardByUri(request, response, "formMail.getemail.do?tableid=" + tableid + "&recordid=" + recordid + "&where=obj.type='" + mailTemplateType + "'");

                    return;

                }
                this.forward(request, response, "eSign");
        }catch (Exception e){
            Log.error("err",e);
        }
    }

    public void eSignRegister(HttpServletRequest request, HttpServletResponse response) throws IOException {
        try {


            GsonBuilder gsonBuilder = new GsonBuilder();
            gsonBuilder.registerTypeAdapter(new TypeToken<Map<String, Object>>() {
            }.getType(), new MapDeserializerDoubleAsIntFix());
            Gson gson = gsonBuilder.create();
            String projectId = SessUtil.getSessInfo().getProjectid();
//            Map userMap = SessUtil.getSessInfo().getUser();
            DAODataMng daoDataMng = new DAODataMng(projectId);
            String esignInstanceJson = request.getParameter("eSigndata");


//            Long esignInstanceId = Long.valueOf(request.getParameter("esignInstanceId"));
            String signersJson = request.getParameter("signersJson");
            String tableid = request.getParameter("tableid");
            String signfuncid = request.getParameter("signfuncid");
            Long recordid = Long.valueOf(request.getParameter("recordid"));

            Map currentUserMap = SessUtil.getSessInfo().getUser();

            Map esignInstanceMap = gson.fromJson(esignInstanceJson, new TypeToken<Map<String, Object>>() {
            }.getType());

            String signFile = (String) esignInstanceMap.get("signfile");

            String signLanguage = (String) esignInstanceMap.get("signLanguage");

            String signPageFile = (String) esignInstanceMap.get("signpagefile");
            String currentRealname = (String) currentUserMap.get("realname");

            String signFlowExpireTimeStr = (String) esignInstanceMap.get("signFlowExpireTime");

            SimpleDateFormat DateFormatYMDHMS = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            esignInstanceMap.put("signFlowExpireTime", DateFormatYMDHMS.parse(signFlowExpireTimeStr));


            esignInstanceMap.put("file_author", currentRealname);

//            String signPageFile = request.getParameter("signPage");


            AttachDAO attachDAO = new AttachDAO(projectId);

            String[] signFilePdfArr = wordToPdf(signFile, tableid, attachDAO, projectId);

            if (ArrayUtils.isEmpty(signFilePdfArr)) {

                response.setCharacterEncoding("UTF-8");
                response.getOutputStream().write("文件格式错误，请上传word或pdf文件！".getBytes(StandardCharsets.UTF_8));
                response.getOutputStream().close();
            }

            String fileName = signFilePdfArr[0];
            String fileLocalUri = signFilePdfArr[1];


            if (StringUtils.isNotEmpty(signPageFile)) {
                String[] signPageFilePdfArr = wordToPdf(signPageFile, tableid, attachDAO, projectId);

                if (ArrayUtils.isEmpty(signPageFilePdfArr)) {
                    response.getOutputStream().write("文件格式错误，请上传word或pdf文件！".getBytes(StandardCharsets.UTF_8));
                    response.getOutputStream().close();
                }
                fileLocalUri = PDFMerger.PDFMerger(signFilePdfArr[1], signPageFilePdfArr[1], signPageFilePdfArr[0]);
                fileName = signPageFilePdfArr[0];

            }

            File fileLocal = new File(fileLocalUri);


            List<Map<String, String>> DAOLightpdfSignIntegrateList = DAOLightpdfSignIntegrate.listRule(projectId, "1");
            Map<String, String> DAOLightpdfSignIntegrateMap = DAOLightpdfSignIntegrateList.get(0);
            String esignUrl = DAOLightpdfSignIntegrateMap.get(DAOLightpdfSignIntegrate.esignUrl);

            String md5 = generateMD5(fileLocal.getPath());
            esignInstanceMap.put("file_code", md5);


            String uploadFileMsgJson = LightpdfSignIntegrateUtil.uploadFile(esignUrl + "/api/files/upload", fileLocal.getPath(), fileName.replace(" ", ""));

            if (StringUtils.equals(uploadFileMsgJson, "408")) {
                response.getOutputStream().write("连接超时，请联系管理员。".getBytes(StandardCharsets.UTF_8));
                response.getOutputStream().close();
                return;
            }


            Map uploadFileMsgMap = gson.fromJson(uploadFileMsgJson, new TypeToken<Map<String, Object>>() {
            }.getType());


            if (!StringUtils.equals(String.valueOf(uploadFileMsgMap.get("status")), "200")) {
                response.getOutputStream().write(String.valueOf(uploadFileMsgMap.get("message")).getBytes(StandardCharsets.UTF_8));
                response.getOutputStream().close();
                return;
            }


            Map uploadFileDataMap = (Map) uploadFileMsgMap.get("data");
            List uploadFileDataList = (List) uploadFileDataMap.get("items");
            Map uploadFileData0Map = (Map) uploadFileDataList.get(0);
            String eSignfileKey = (String) uploadFileData0Map.get("file_id");


            DtrefDAO dtrefDAO = new DtrefDAO(projectId);

            String eSignDataRefField = dtrefDAO.getRefField("xsht", tableid);

            Map eSignDataMap = daoDataMng.getRecord(tableid, Long.valueOf(recordid));

            Long studyId = (Long) eSignDataMap.get(eSignDataRefField);

            Map<String, String> eSignEngineMap = daoDataMng.getRecord("esign_engine", Long.valueOf(signfuncid));

            List<Map<String, String>> signersList = gson.fromJson(signersJson, new TypeToken<List<Map<String, String>>>() {
            }.getType());


            ArrayList<Map> signerInfosList = getSignerInfosList(fileLocal, signersList);

            Map esignInstanceToSaveMap = new HashMap();
            esignInstanceToSaveMap.put("study_id", studyId);
            esignInstanceToSaveMap.put("language", signLanguage);
            esignInstanceToSaveMap.put("remark", esignInstanceMap.get("remark"));
            esignInstanceToSaveMap.put("subject", esignInstanceMap.get("subject"));
            esignInstanceToSaveMap.put("sign_expire_data", esignInstanceMap.get("signFlowExpireTime"));
            esignInstanceToSaveMap.put("initiator", currentUserMap.get("username"));
            daoDataMng.save("esign_instance", esignInstanceToSaveMap);
            Long esignInstanceId = (Long) esignInstanceToSaveMap.get("id");
            //构造文件存储链接 https://meduap-tst.hengrui.com:8085/home.jsp?projectid=cdtmsen_val&gotopage=/uapvue/index.html#/normalform?tableid=esign_instance&recordid=4045438983
            String urlSufix = String.format("/uapvue/index.html#/normalform?tableid=esign_instance&recordid=%d", esignInstanceId);
            urlSufix=URLEncoder.encode(urlSufix);
            esignInstanceMap.put("urlSufix",urlSufix);
            String CNSMsg = LightpdfSignIntegrateUtil.eSignRegister(projectId, eSignfileKey, esignInstanceMap, signerInfosList, DAOLightpdfSignIntegrateMap,eSignEngineMap,signLanguage,studyId);


            Map CNSMsgMap = gson.fromJson(CNSMsg, new TypeToken<Map<String, Object>>() {
            }.getType());

            if (!StringUtils.equals(String.valueOf(CNSMsgMap.get("status")), "200")) {
                String message = (String) CNSMsgMap.get("message");
                response.getOutputStream().write(message.getBytes(StandardCharsets.UTF_8));
                response.getOutputStream().close();
                return;

            }


            Map GUUDataMap = (Map) CNSMsgMap.get("data");
            String signFlowId = (String) GUUDataMap.get("task_id");


            String fileSelected = request.getParameter("fileSelected");
            String content = request.getParameter("content");
            esignInstanceToSaveMap.put("content", content);

            esignInstanceToSaveMap.put("selected_file", fileSelected);

            esignInstanceToSaveMap.put("tableid", tableid);
            esignInstanceToSaveMap.put("type", "project");
            esignInstanceToSaveMap.put("recordid", recordid);
            esignInstanceToSaveMap.put("active", "1");
            esignInstanceToSaveMap.put("study_id", studyId);
//            esignInstanceToSaveMap.put("edition_name", eSignDataMap.get(eSignEngineMap.get("edition_filed")));
//            esignInstanceToSaveMap.put("edition_date", eSignDataMap.get(eSignEngineMap.get("edition_date_filed")));
//            esignInstanceToSaveMap.put("status", "0");
            esignInstanceToSaveMap.put("esign_engine_id", eSignEngineMap.get("id"));
            esignInstanceToSaveMap.put("sign_flow_id", signFlowId);
            esignInstanceToSaveMap.put("status", "1");
            esignInstanceToSaveMap.put("esign_msg", CNSMsg);

            esignInstanceToSaveMap.put("language", signLanguage);


            esignInstanceToSaveMap.put("remark", esignInstanceMap.get("remark"));
            esignInstanceToSaveMap.put("subject", esignInstanceMap.get("subject"));
            esignInstanceToSaveMap.put("sign_expire_data", esignInstanceMap.get("signFlowExpireTime"));
            esignInstanceToSaveMap.put("initiator", currentUserMap.get("username"));
            esignInstanceToSaveMap.put("esign_begin_date", new Date());
            esignInstanceToSaveMap.put("file_name", fileName);
            daoDataMng.save("esign_instance", esignInstanceToSaveMap);


            ArrayList<Map> signUrlInfosToSaveList = new ArrayList<>();
            for (Map signUrlInfosMap : signerInfosList) {
                Map signUrlInfosToSaveMap = new HashMap<>();
                signUrlInfosToSaveMap.put("name", signUrlInfosMap.get("name_zh"));
                String email = (String) signUrlInfosMap.get("email");
                signUrlInfosToSaveMap.put("user_code", new String(Base64.getDecoder().decode(email.getBytes())));
                signUrlInfosToSaveMap.put("verification_code", signUrlInfosMap.get("verification_code"));
                signUrlInfosToSaveMap.put("esign_url", signUrlInfosMap.get("signUrlShort"));
                signUrlInfosToSaveMap.put("esign_instance_id", esignInstanceToSaveMap.get("id"));

                signUrlInfosToSaveMap.put("status", "5");

                signUrlInfosToSaveMap.put("signer_id", String.valueOf(signUrlInfosMap.get("signer_id")));

                signUrlInfosToSaveList.add(signUrlInfosToSaveMap);
//				daoDataMng.save("esign_signer",signUrlInfosToSaveMap );
//				daoDataMng.saveBatch("esign_signer", (List) signUrlInfosToSaveList, 2L,null);


            }

            Long userid = Long.valueOf(SessUtil.getSessInfo().getUserid());
            daoDataMng.saveBatch("esign_signer", signUrlInfosToSaveList, userid, null);


            Map<Object, Object> esignFileMap = new HashMap<>();


            esignFileMap.put("tableid", tableid);
            esignFileMap.put("recordid", recordid);
            esignFileMap.put("study_id", studyId);
            esignFileMap.put("record_file_value", signFile + "|");
            esignFileMap.put("esign_file_key", eSignfileKey);
            String fileLocalUuid = attachDAO.saveFile(fileLocal, "esign_file");
            String strAttachToSave = "" + fileName + "*" + fileLocalUuid + "|";
            esignFileMap.put("esign_file_key", eSignfileKey);
            esignFileMap.put("file", strAttachToSave);
            esignFileMap.put("file_md5",md5);


            esignFileMap.put("esign_instance_id", esignInstanceToSaveMap.get("id"));

            daoDataMng.save("esign_file", esignFileMap);


            Map esignLogToSaveMap = new HashMap<>();
            esignLogToSaveMap.put("esign_instance_id", esignInstanceToSaveMap.get("id"));
            esignLogToSaveMap.put("tableid", tableid);
            esignLogToSaveMap.put("recordid", recordid);
            esignLogToSaveMap.put("study_id", studyId);
            esignLogToSaveMap.put("esign_status", "0");
//            esignLogToSaveMap.put("msg", ReceiveMsg);
//            esignLogToSaveMap.put("esign_desc", callBackDesc);
            esignLogToSaveMap.put("execute_date", new Date());
            esignLogToSaveMap.put("name", currentUserMap.get("username"));


            daoDataMng.save("esign_log", esignLogToSaveMap);

            Map<Object, Object> responseData = new HashMap<>();


            String redirectUrl = esignUrl + "/set-sign?file_id=" + eSignfileKey + "&task_id=" + signFlowId;
            responseData.put("setUrl", redirectUrl);
//            responseData.put("redirectUrl","/tableapp.edit.do?tableid=esign_instance&id="+esignInstanceToSaveMap.get("id"));


            ResponseUtils.response(response, HttpStatus.OK, responseData);
//            response.getOutputStream().write("200".getBytes());
//            response.getOutputStream().close();

            //StringUtils.equals(,'1')
            String isCreateAutoProcessWorkflow = (String) eSignEngineMap.get("is_create_wf_auto_process");
            if (StringUtils.equals(isCreateAutoProcessWorkflow,"1")) {
                String todofuncid = getFuncidForward(projectId,tableid,String.valueOf(recordid));
                PublicworkflowUtil.process(projectId, tableid, String.valueOf(recordid), todofuncid, null, null, null, null, "", SessUtil.getSessInfo().getUserid());
            }

            return;

        } catch (Exception e) {
            Log.error("", e);

        } finally {

        }
    }

    public static String generateMD5(String filePath) throws Exception {
        MessageDigest md = MessageDigest.getInstance("MD5");
        try (DigestInputStream dis = new DigestInputStream(new FileInputStream(filePath), md)) {
            // 读取文件内容并更新MD5摘要
            while (dis.read() != -1) {
                // 什么都不做，只需读取数据以更新摘要
            }
        }

        // 获取生成的MD5值
        byte[] digest = md.digest();
        StringBuilder sb = new StringBuilder();
        for (byte b : digest) {
            sb.append(String.format("%02x", b & 0xff));
        }

        return sb.toString();
    }

//    1681868007

    public static ArrayList<Map> getSignerInfosList(File fileLocal, List<Map<String, String>> signersList) throws IOException {
        File esign_file = fileLocal;

//        String absolutePath = esign_file.getAbsolutePath();
//        PdfReader pdfReader = new PdfReader(absolutePath);
//        float[] xArr = getKeyWords(pdfReader, "签名");
//        Float X = ArrayUtils.isEmpty(xArr) ? null : xArr[0];
        ArrayList<Map> signerInfosList = new ArrayList<>();
//        ArrayList<String> existRandomCode = new ArrayList<>();


        for (Map signerMap : signersList) {
            String name = String.valueOf(signerMap.get("name")).split("\\(")[0];
            Log.info("-----------------------------分割收件人邮箱字符串获取到的收件人姓名是："+name);

//            float[] coordinate = getKeyWords(pdfReader, name);
//            Long page = ArrayUtils.isEmpty(coordinate) ? null : (long) coordinate[2];
//            Float Y = ArrayUtils.isEmpty(coordinate) ? null : coordinate[1];
//            Float X = ArrayUtils.isEmpty(coordinate) ? null : coordinate[0] + 100;

//                HashMap<Object, Object> coordinateMap = new HashMap<>();
            Map signerInfosMap = new HashMap<>();
            String email = (String) signerMap.get("value");

//            signerInfosMap.put("page", page);
//            signerInfosMap.put("position_x", X);
//            signerInfosMap.put("position_y", Y);

//                Log.info("name:" + name + ",page:" + page + ",X:" + X + ",Y:" + Y);

//                signerInfosMap.put("coordinate",coordinateMap);
            signerInfosMap.put("email", new String(Base64.getEncoder().encode(email.getBytes(StandardCharsets.UTF_8))));
            signerInfosMap.put("email_2", email);
//            String randomCode;
//            while (true) {
//                randomCode = generateRandomCode();
//                if (!existRandomCode.contains(randomCode)) {
//                    existRandomCode.add(randomCode);
//                    break;
//                }
//            }

            String randomCode = generateRandomCode();
            signerInfosMap.put("verification_code", randomCode);
            signerInfosMap.put("name", name);
            signerInfosList.add(signerInfosMap);
        }
//        pdfReader.close();

        return signerInfosList;
    }

    public void idVerify(HttpServletRequest request, HttpServletResponse response) {
        try {


            String requestBody = request.getReader().lines().collect(Collectors.joining(System.lineSeparator()));


            Gson gson = new Gson();
            Map<String, String> requestDataMap = gson.fromJson(requestBody, new TypeToken<Map<String, String>>() {
            }.getType());

            String id = requestDataMap.get("id");
            String taskId = requestDataMap.get("taskId");
            String email = requestDataMap.get("email");
            String verificationCode = requestDataMap.get("verificationCode");
            String signatureReason = requestDataMap.get("signatureReason");
            String system = requestDataMap.get("system");
            String language = requestDataMap.get("language");
            String token = requestDataMap.get("token");
            DAODataMng daoDataMng = new DAODataMng(system);
            Map reData = new HashMap<>();

            Map esignAccountMap = (Map) daoDataMng.listRecord("esign_account", "obj.email='" + email + "'", null, 1).get(0);

            if (!StringUtils.equals(verificationCode, (String) esignAccountMap.get("password"))) {
                ResponseUtils.response(response, HttpStatus.NOT_FOUND);
            }

            if(StringUtils.isNotEmpty(token)){
                String url = requestDataMap.get("url");
                String sessStr = URLUtil.getContentUTF8(url+"/lightpdfSign.checktoken.do?token="+token, 3000);
                Map<String, Object> su = gson.fromJson(sessStr, Map.class);

//            BeanSessUser su = new Gson().fromJson(sessStr, BeanSessUser.class);
                if(!String.valueOf(su.get("status")).equals("200")) {
                    response.getOutputStream().write("Token err".getBytes());
                    return;

                }

                Map currUserInfoMap = (Map) su.get("mapInfo");
                String redirectUrl = (String) currUserInfoMap.get("redirectUrl");

                reData.put("redirectUrl", redirectUrl);
                ResponseUtils.response(response, HttpStatus.OK, reData);
                return;


            }


            List<Map> esignSignerList = daoDataMng.listRecord("esign_signer", "obj.esign_instance_id='" + id + "' and obj.user_code='" + email + "' and obj.status!=9", null, 1);


            if (CollectionUtils.isNotEmpty(esignSignerList)) {
                email = new String(Base64.getEncoder().encode(email.getBytes(StandardCharsets.UTF_8)));

//                Map<String, Object> esignSignerToSaveMap = new HashMap<>();
//                esignSignerToSaveMap.put("sign_reason", signatureReason);
//                esignSignerToSaveMap.put("id", esignSignerList.get(0).get("id"));
//                daoDataMng.save("esign_signer", esignSignerToSaveMap);
                List<Map<String, String>> listR = DAOLightpdfSignIntegrate.listRule(system, "1");
                String esignUrl = listR.get(0).get(DAOLightpdfSignIntegrate.esignUrl);

//                String language=


                String redirectUrl = esignUrl + (StringUtils.equals(language, "en") ? "/en" : "") + "?task_id=" + taskId + "&email=" + email + "&verification_code=" + esignSignerList.get(0).get("verification_code");

                reData.put("redirectUrl", redirectUrl);
                ResponseUtils.response(response, HttpStatus.OK, reData);
                return;
            } else {
                ResponseUtils.response(response, HttpStatus.NOT_FOUND);
            }


            return;


        } catch (IOException ex) {
            throw new RuntimeException(ex);
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }


    }

    public static void main2(String[] args) {
//        HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
//
//        objectObjectHashMap.put("status","200");
//        objectObjectHashMap.put("msg","success");
//
//
//        ArrayList<String> objects = new ArrayList<>();
//        objects.add("aaaaaa1");
//        objects.add("aaaaaa2");
//        objects.add("aaaaaa3");
//        Gson gson = new Gson();
//        objectObjectHashMap.put("data",objects);
//        System.out.println(gson.toJson(objectObjectHashMap));


        String reason = "我批准";

        String sign_reason = null;
        if (StringUtils.contains(reason, "作者")) sign_reason = "author";
        if (StringUtils.contains(reason, "审核")) sign_reason = "review";
        if (StringUtils.contains(reason, "批准")) sign_reason = "approve";
        if (StringUtils.contains(reason, "负责")) sign_reason = "responsible";

//        System.out.println(sign_reason);


    }

    public void register(HttpServletRequest request, HttpServletResponse response) {
        try {

            String locStr = null;
            String system = request.getParameter("system");
            request.setAttribute("system", system);

            Cookie[] ckA = request.getCookies();
            if (ckA != null) {
                for (int i = 0; i < ckA.length; ++i) {
                    Cookie ck = ckA[i];
                    String ckname = ck.getName();
                    if (ckname != null && ckname.equalsIgnoreCase("signlang")) {
                        String locStrInCookie = ck.getValue().toLowerCase();
                        if (locStrInCookie != null && !locStrInCookie.equals("")) {
                            locStr = locStrInCookie;
                            break;
                        }
                    }
                }
            }

            if (locStr == null) {
                locStr = "zh";
            }
            request.setAttribute("SYSTEM_LANGUAGE", locStr);
            this.forward(request, response, "register");

//            String requestBody = request.getReader().lines().collect(Collectors.joining(System.lineSeparator()));
//            Gson gson = new Gson();
//            Map<String, String> requestDataMap = gson.fromJson(requestBody, new TypeToken<Map<String, String>>() {
//            }.getType());
//            String taskId = requestDataMap.get("taskId");
//            String email = requestDataMap.get("email");

        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }


    }

    public void resetpwdsubmit(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String email = request.getParameter("email");
        String checkcode = request.getParameter("checkcode");
        String newpwd = request.getParameter("newpwd");
        String system = request.getParameter("system");
        // 获取新增的参数
        String englishName = request.getParameter("englishName");
        String name = request.getParameter("name");
        Log.info("获取到签字密码设置的参数邮箱：" + email + "验证码 " + checkcode + "密码 " + newpwd + "系统 " + system + "英文名 " + englishName + "中文名 " + name);
        String codeInCache = "";
        String t = (String) mapCodeTimeCache.get(email);
        if (t != null && new Date().getTime() - Long.valueOf(t).longValue() < 600000) {
            codeInCache = (String) mapCodeCache.get(email);
        }

        DAODataMng daoDataMng = new DAODataMng(system);
        if (checkcode != null && !checkcode.equals("") && (checkcode).equals(codeInCache)) {

          //  List<Map> esignAccountList = daoDataMng.listRecord("esign_account", "obj.email='" + email + "'", null, 1);
            String condition = "obj.email='" + email + "'";

            // 增加新条件后的拼接方式
       //     String newCondition = condition + " and obj.name_zh is not null  and obj.name_en is not null ";

            List<Map> esignAccountList = daoDataMng.listRecord("esign_account", condition, null, 1);

            if (CollectionUtils.isEmpty(esignAccountList)) {

                HashMap<Object, Object> esignAccountToSaveMap = new HashMap<>();
                esignAccountToSaveMap.put("email", email);
                esignAccountToSaveMap.put("password", newpwd);
                esignAccountToSaveMap.put("name_en", englishName);
                esignAccountToSaveMap.put("name_zh", name);
                daoDataMng.saveRecord("esign_account", esignAccountToSaveMap);

            } else {

                esignAccountList.get(0).put("password", newpwd);
                esignAccountList.get(0).put("name_en", englishName);
                esignAccountList.get(0).put("name_zh", name);
                daoDataMng.save("esign_account", esignAccountList.get(0));

            }
            Log.info("重置密码校验返回了OK!");
            response.getOutputStream().write("OK".getBytes());
            return;
        } else {
            Log.info("验证码: " + checkcode + "不等于缓存的验证码:" + codeInCache);
            response.getOutputStream().write(this.getLanguage().get("验证码输入有误！").getBytes(StandardCharsets.UTF_8));
            // this.gotoMsgWin(request, response,"");
        }
    }

    public Map<String, String> saveBackFile(String projectId, String outputUrl, String tableid, Long recordid, Long esignEngineId, Map esignFileMap,Integer callBackEnum) {

        try {
            DAODataMng daoDataMng = new DAODataMng(projectId);


            String fileIndex = (String) esignFileMap.get("file");
            String fileName = fileIndex.split("\\*")[0];

            Map esignFileTOSaveMap = new HashMap<>();


            String signedFileKey = outputUrl.substring(outputUrl.lastIndexOf('/') + 1);
            String filePath = WebPath.getRootPath() + DAOFileup.tempfolder + "/eSign";

            LightpdfSignIntegrateUtil.downloadByNIO(outputUrl, filePath, signedFileKey);

            esignFileTOSaveMap.put("esign_signed_file_key", signedFileKey);
            esignFileTOSaveMap.put("id", esignFileMap.get("id"));
            AttachDAO attachDAO = new AttachDAO(projectId);


            String backStrAttachToSave = "";


            File fileLocal = new File(filePath + "/" + signedFileKey);

            String fileUuid = attachDAO.saveFile(fileLocal, "esign_file");
            String strAttachToSave = "" + fileName.split("\\*")[0] + "*" + fileUuid + "|";
            esignFileTOSaveMap.put("signed_file", strAttachToSave);
            esignFileTOSaveMap.put("signed_file_md5", generateMD5(filePath + "/" + signedFileKey));
            daoDataMng.save("esign_file", esignFileTOSaveMap);

            Map signEngineMap = daoDataMng.getRecord("esign_engine", esignEngineId);
            String toSignField="";
            String backFileAddedMethod="";
            if(!MapUtils.isEmpty(signEngineMap)){
                 toSignField = (String) signEngineMap.get("to_sign_field");
                 backFileAddedMethod = (String) signEngineMap.get("back_file_added_method");
            }
            if(!ObjectUtils.isEmpty(tableid)&&!ObjectUtils.isEmpty(recordid)&&callBackEnum == 4){
                Map signDataMap = daoDataMng.getRecord(tableid, recordid);
                String backFileUuid = attachDAO.saveFile(fileLocal, tableid);
                backStrAttachToSave += "" + fileName.split("\\*")[0] + "*" + backFileUuid + "|";
                String recordFileValue = (String) esignFileMap.get("record_file_value");
                String toSignValue ="";
                if(!toSignField.isEmpty()){
                    toSignValue = (String) signDataMap.get(toSignField);
                }

                if (StringUtils.equals(backFileAddedMethod,"2")) {
                    toSignValue = backStrAttachToSave+toSignValue;
                }else {
                    toSignValue = toSignValue.replace(recordFileValue, backStrAttachToSave);
                }
                signDataMap.put(toSignField, toSignValue);
                daoDataMng.saveRecord(tableid, signDataMap, "2", null);
            }

            Map<String, String> FileInfoMap = new HashMap<>();
            FileInfoMap.put("filePath", filePath + "/" + signedFileKey);
            FileInfoMap.put("fileName", fileIndex.split("\\*")[0]);
            return FileInfoMap;
        } catch (IOException e) {

            Log.error("", e);

        } catch (Exception e) {
            Log.error("", e);
        }


        return null;
    }

//    public static void main(String[] args) throws UnsupportedEncodingException {
////        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
////
////        Date date = new Date(1686208530 * 1000l);
////        System.out.println(sdf.format(date));
//
//        Date date = new Date();
//        SimpleDateFormat sdf = new SimpleDateFormat("yyMMddHHmm");
//        String formattedDate = sdf.format(date);
//        System.out.println(formattedDate);
//    }

    public void sendcheckcode(HttpServletRequest request, HttpServletResponse response) throws Exception {

        String email = request.getParameter("email");
        String language = request.getParameter("language");
        String system = request.getParameter("system");
        String errinfo = "";

        try {
            email = email.trim();


            // String pwdwrongnum = new UserSynUtil().getpwdwrongnum(projectid, loginid);
            // Log.info("pwdwrongnum "+pwdwrongnum);
            // if (pwdwrongnum != null && !pwdwrongnum.equals("")) {
            // errinfo = pwdwrongnum.replaceAll("<br>", "\r\n");

            // }else {
            String locStr = getlocStr(request);

            ResourceBundle bundle = ResourceBundle.getBundle(ActionLightpdfSignIntegrate.class.getPackage().getName() + ".i18n." + language);

            String code = String.valueOf(new Date().getTime());
            code = code.substring(code.length() - 6);
            mapCodeCache.put(email, code);
            mapCodeTimeCache.put(email, String.valueOf(new Date().getTime()));
            String note = bundle.getString("checkcodemail.content1") + code
                    + bundle.getString("checkcodemail.content2");
            // Log.info(email+" "+note);
            ThreadSendmail.addTask(system, bundle.getString("checkcodemail.title"), note, email);


            response.getOutputStream().write("OK".getBytes());
            response.getOutputStream().close();
            return;

            // }


        } catch (Exception e) {
            errinfo = this.getLanguage().get("发送失败：") + e.getMessage();
            Log.error("", e);
        }
        if (errinfo.equals("")) {
            errinfo = this.getLanguage().get("必须录入登录ID和邮箱");
        }
        response.getOutputStream().write(errinfo.getBytes(StandardCharsets.UTF_8));
        response.getOutputStream().close();
    }
    public void RemoveSigner(HttpServletRequest request, HttpServletResponse response) {

        try {
            String singerid = request.getParameter("id");

            String projectid = SessUtil.getSessInfo().getProjectid();


            List<Map<String, String>> DAOLightpdfSignIntegrateList = DAOLightpdfSignIntegrate.listRule(projectid, "1");

            Map<String, String> DAOLightpdfSignIntegrateMap = DAOLightpdfSignIntegrateList.get(0);

            String CDTMSUrl = DAOLightpdfSignIntegrateMap.get(DAOLightpdfSignIntegrate.CDTMSUrl);
            DAODataMng daoDataMng = new DAODataMng(projectid);


            Map esignSignerMap = daoDataMng.getRecord("esign_signer", Long.valueOf(singerid));

            String email = (String) esignSignerMap.get("user_code");

            Map esignInstanceMap = daoDataMng.getRecord("esign_instance", (Long) esignSignerMap.get("esign_instance_id"));


            String fileName = (String) esignInstanceMap.get("file_name");



            String signerId = (String) esignSignerMap.get("signer_id");
            String signFlowId = (String) esignInstanceMap.get("sign_flow_id");

            GsonBuilder gsonBuilder = new GsonBuilder();
            gsonBuilder.registerTypeAdapter(new TypeToken<Map<String, Object>>() {
            }.getType(), new MapDeserializerDoubleAsIntFix());
            Gson gson = gsonBuilder.create();
            String esignUrl = DAOLightpdfSignIntegrateMap.get(DAOLightpdfSignIntegrate.esignUrl);

            String SCSMsg = LightpdfSignIntegrateUtil.httpDelete(esignUrl + "/api/tasks/" + signFlowId + "/users/" + signerId);
            Map SCSMsgMap = gson.fromJson(SCSMsg, new TypeToken<Map<String, Object>>() {
            }.getType());

            HashMap<Object, Object> signerToSaveMap = new HashMap<>();
            signerToSaveMap.put("status", "9");
            signerToSaveMap.put("id", esignSignerMap.get("id"));
            daoDataMng.save("esign_signer", signerToSaveMap);
            String content = "\"" + fileName.replace(".pdf", "") + "\" ，您被移除签署人列表.";

            LightpdfSignIntegrateUtil.httpPut(esignUrl + "/api/tasks/" + signFlowId, "{\"type\":1}");

            ThreadSendmail.addTask(projectid, "《" + fileName.replace(".pdf", "") + "》 您被移除签署人列表!", content, email);

            ResponseUtils.response(response, HttpStatus.OK);







        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }
    public void setSigner(HttpServletRequest request, HttpServletResponse response) {
        try {


            String requestBody = request.getReader().lines().collect(Collectors.joining(System.lineSeparator()));
            GsonBuilder gsonBuilder = new GsonBuilder();
            gsonBuilder.registerTypeAdapter(new TypeToken<Map<String, Object>>() {
            }.getType(), new MapDeserializerDoubleAsIntFix());
            Gson gson = gsonBuilder.create();
            Map<String, String> requestDataMap = gson.fromJson(requestBody, new TypeToken<Map<String, String>>() {
            }.getType());

            String id = requestDataMap.get("id");
            String recipients = requestDataMap.get("recipients");




            String projectid = SessUtil.getSessInfo().getProjectid();

            DAODataMng daoDataMng = new DAODataMng(projectid);


            List<Map<String, Object>> esignSignerList = daoDataMng.listRecord("esign_signer", "obj.esign_instance_id=" + id + "  and obj.status in ('0','1','5')", null, 1000);

            String currRecipientsStr = "";
            for (Map esignSignerMap : esignSignerList) {

                String name = (String) esignSignerMap.get("name");
                String email = (String) esignSignerMap.get("user_code");
                currRecipientsStr += name + "<" + email + ">;";

            }

            String[] reqRecipientsArr = recipients.split(";");

            String[] currRecipientsArr = currRecipientsStr.split(";");


//            Gson gson = new Gson();

            List<String> reqRecipientsList = Arrays.asList(reqRecipientsArr);
            List<String> currRecipientsList = Arrays.asList(currRecipientsArr);

            // Find elements present in currRecipientsList but not in reqRecipientsList
            List<String> addedRecipients = new ArrayList<>(reqRecipientsList);
            addedRecipients.removeAll(currRecipientsList);


            List<Map<String, String>> DAOLightpdfSignIntegrateList = DAOLightpdfSignIntegrate.listRule(projectid, "1");

            Map<String, String> DAOLightpdfSignIntegrateMap = DAOLightpdfSignIntegrateList.get(0);

            String esignUrl = DAOLightpdfSignIntegrateMap.get(DAOLightpdfSignIntegrate.esignUrl);


            Map esignInstanceMap = daoDataMng.getRecord("esign_instance", Long.valueOf(id));

            String signFlowId = (String) esignInstanceMap.get("sign_flow_id");


            Map<Object, Object> responseData = new HashMap<>();

            if (CollectionUtils.isNotEmpty(addedRecipients)) {

                for (String recipient : addedRecipients) {

                    HashMap<Object, Object> signerInfoMap = new HashMap<>();


                    String[] recipientArr = recipient.trim().split("[<>]");
                    String name = recipientArr[0].trim();
                    String email = recipientArr.length > 1 ? recipientArr[1].trim() : recipientArr[0].trim();

                    signerInfoMap.put("email", new String(Base64.getEncoder().encode(email.getBytes(StandardCharsets.UTF_8))));
                    signerInfoMap.put("verification_code", generateRandomCode());
                    signerInfoMap.put("is_author", 0);

                    String condition = "obj.email='" +email+ "'";

                    // 增加新条件后的拼接方式
                    String newCondition = condition + " and obj.name_zh is not null  and obj.name_en is not null ";

                    List<Map> esignAccountList = daoDataMng.listRecord("esign_account", newCondition, null, 1);
                    if (CollectionUtils.isNotEmpty(esignAccountList)&&!StringUtils.isEmpty(esignAccountList.get(0).get("name_en").toString())&&!StringUtils.isEmpty(esignAccountList.get(0).get("name_zh").toString())) {
                        signerInfoMap.put("name_en", esignAccountList.get(0).get("name_en"));
                        signerInfoMap.put("name_zh", esignAccountList.get(0).get("name_zh"));
                    }else{
                        signerInfoMap.put("name_en", name);
                        signerInfoMap.put("name_zh", name);
                    }



                    String SCSMsg = LightpdfSignIntegrateUtil.sendPost(esignUrl + "/api/tasks/" + signFlowId + "/users", gson.toJson(signerInfoMap), projectid);

                    Map SCSMsgMap = gson.fromJson(SCSMsg, new TypeToken<Map<String, Object>>() {
                    }.getType());

                    if (!StringUtils.equals(String.valueOf(SCSMsgMap.get("status")), "200")) {
                        continue;

                    }
                    Map SCSMsgDataMap = (Map) SCSMsgMap.get("data");
                    signerInfoMap.put("signer_id", String.valueOf(SCSMsgDataMap.get("id")));
                    signerInfoMap.put("user_code", email);
                    signerInfoMap.put("esign_instance_id", Long.valueOf(id));
                    signerInfoMap.put("status", "5");
                    daoDataMng.save("esign_signer", signerInfoMap);


                }


                Map esignFileMap = (Map) daoDataMng.listRecord("esign_file", "obj.esign_instance_id=" + id, null, 1).get(0);
                String redirectUrl = esignUrl + "/set-sign?file_id=" + esignFileMap.get("esign_file_key") + "&task_id=" + signFlowId;
                responseData.put("setUrl", redirectUrl);

            }






            String SCSMsg = LightpdfSignIntegrateUtil.httpPut(esignUrl + "/api/tasks/" + signFlowId, "{\"type\":1}");

            ResponseUtils.response(response, HttpStatus.OK, responseData);
            return;


        } catch (Exception e) {
            Log.error("err", e);
        }
    }

    public void signCreate(HttpServletRequest request, HttpServletResponse response) {
        try {
            String projectId = SessUtil.getSessInfo().getProjectid();
            if (LightpdfSignIntegrateUtil.getSignCreateAuth(projectId)) {
                String isNewSign = (String)request.getSession().getAttribute("isNewSign");
                // 如果是新发起签字，清除所有相关的session属性
                if(StringUtils.isEmpty(isNewSign) || !isNewSign.equals("no")) {
                    // 清除所有相关的session属性
                    String[] attributes = {
                            "signedFile", "ReSignerOptions", "ReStudyId", "ReStudyName",
                            "ReLanguage", "ReType", "signedFileName", "isNewSign"
                    };

                    for(String attr : attributes) {
                        request.getSession().removeAttribute(attr);
                    }
                } else {
                    Log.info("补签场景，从session获取属性并设置到request中");
                    // 补签场景：从session获取属性并设置到request中
                    String[] attributes = {
                            "signedFile", "ReSignerOptions", "ReStudyId", "ReStudyName",
                            "ReLanguage", "ReType", "signedFileName"
                    };

                    for(String attr : attributes) {
                        Log.info("补签场景，获取到的属性：" + attr + "，值为：" + request.getSession().getAttribute(attr));
                        request.setAttribute(attr, request.getSession().getAttribute(attr));
                    }
                }

                this.forward(request, response, "signCreate");
            }

        } catch (Exception e) {
            Log.error(e.getMessage(), e);
        }
    }

    public void signerSearch(HttpServletRequest request, HttpServletResponse response) {
        try {


            String term = request.getParameter("q");
            String page = request.getParameter("page");
            String studyid = request.getParameter("param");
            if (term != null && !"".equals(term)) studyid = "";

            String projectid = SessUtil.getSessInfo().getProjectid();
            DAODataMng dmdao = new DAODataMng(projectid);
            DAOUsersyn daoUsersyn = new DAOUsersyn(projectid);

            List<Map<String, String>> daoUsersynRoleList = daoUsersyn.listRule();
            Map<String, String> daoUsersynRoleMap = daoUsersynRoleList.get(0);
            String accountTableId = daoUsersynRoleMap.get(CNT_Usersyn.tableid);
            String nameFileId = "name";
            String emailFileId = "email";

            List<Map<String, Object>> signerList = new ArrayList<>();
            Set emailSet = new HashSet();            //申办方人员  hqb1 CRO人员 zxsyr1   中心研究者 zxsyr   机构人员 zxyfry   CRC人员 zxry
            if (studyid != null && !"".equals(studyid)) {

                if(!"cdtmsen_val".equals(projectid)){
                String roles = "'PM','Medical','Analyst','DM','PM','EDM','RAND','CODER','CTA','CRA'";
                List<Map> list1 = dmdao.listRecord("hqb1", "obj.studyid='" + studyid + "' and obj.js in (" + roles + ")", "", 1000);
                List<Map> list2 = dmdao.listRecord("zxsyr1", "obj.studyid='" + studyid + "' and obj.ryjs in (" + roles + ")", "", 1000);
                List<Map> list3 = dmdao.listRecord("zxsyr", "obj.studyid='" + studyid + "' and obj.rylb in (" + roles + ")", "", 1000);
                List<Map> list4 = dmdao.listRecord("zxyfry", "obj.studyid='" + studyid + "' and obj.js in (" + roles + ")", "", 1000);
                List<Map> list5 = dmdao.listRecord("zxry", "obj.studyid='" + studyid + "' and obj.rylb in (" + roles + ")", "", 1000);

                List listtemp = new ArrayList();
                list1.stream().forEach(map -> {
                    emailSet.add(map.get("email"));
                    Map mapV = new HashMap();
                    mapV.put("name", map.get("xm"));
                    mapV.put("email", map.get("email"));
                    listtemp.add(mapV);
                });
                list2.stream().forEach(map -> {
                    emailSet.add(map.get("lxryj"));
                    Map mapV = new HashMap();
                    mapV.put("name", map.get("sbfywaqzy"));
                    mapV.put("email", map.get("lxryj"));
                    listtemp.add(mapV);
                });
                list3.stream().forEach(map -> {
                    emailSet.add(map.get("yx"));
                    Map mapV = new HashMap();
                    mapV.put("name", map.get("xm"));
                    mapV.put("email", map.get("yx"));
                    listtemp.add(mapV);
                });
                list4.stream().forEach(map -> {
                    emailSet.add(map.get("email"));
                    Map mapV = new HashMap();
                    mapV.put("name", map.get("xm"));
                    mapV.put("email", map.get("email"));
                    listtemp.add(mapV);
                });
                list5.stream().forEach(map -> {
                    emailSet.add(map.get("yx"));
                    Map mapV = new HashMap();
                    mapV.put("name", map.get("xm"));
                    mapV.put("email", map.get("yx"));
                    listtemp.add(mapV);
                });
                signerList = listtemp;


            }else{

                    List listtemp = new ArrayList();

                    String whereC = "studyid='" + studyid + "' or studyid is null";
                    DAODbview dbview = new DAODbview(projectid);
                    int index = dbview.getRuleIndexByName("项目参与人");
                    List listC = DbviewUtil.getDataList(projectid, index + "", whereC, "", 999, 1);
                    if (listC != null && listC.size() > 0) {
                        for(int i = 0; i < listC.size(); ++i) {
                            Map map = (Map)listC.get(i);
                            String name = (String)map.get("name");
                            String email = (String)map.get("email");
                            Map mapV = new HashMap();
                            mapV.put("name", name);
                            mapV.put("email", email);
                            listtemp.add(mapV);

                        }
                    }
                    signerList = listtemp;

                }
            } else {
                 nameFileId = daoUsersynRoleMap.get(CNT_Usersyn.field_name);
                 emailFileId = daoUsersynRoleMap.get(CNT_Usersyn.field_email);

                signerList = dmdao.listRecord(accountTableId, "obj." + emailFileId + " like '%" + term.replace("'","\\'") + "%' or obj." + nameFileId + " like '%" + term.replace("'","\\'") + "%'", null, 10, StringUtils.isNotEmpty(page) ? Integer.parseInt(page) : 1);
            }

            String currUserid = SessUtil.getSessInfo().getUserid();
            List<Map<String, Object>> options = new ArrayList<>();

            if (CollectionUtils.isEmpty(signerList)) {

                emailFileId = "user_code";
                nameFileId = "name";
                signerList = dmdao.listRecord("esign_signer", "obj.userid=" + currUserid + " and (obj.user_code like '%" + term.replace("'","\\'") + "%' or obj.name like '%" + term.replace("'","\\'") + "%') and obj.name not like '%@%'", null, 10, StringUtils.isNotEmpty(page) ? Integer.parseInt(page) : 1);


                String finalEmailFileId = emailFileId;
                signerList = signerList.stream()
                        .collect(Collectors.toMap(
                                signerMap -> signerMap.get(finalEmailFileId),   // 根据键的值进行分组
                                Function.identity(),  // 保留原始Map
                                (existing, replacement) -> existing // 处理重复的情况，保留先出现的Map
                        ))
                        .values().stream()
                        .collect(Collectors.toList());


            } else {
                if (emailSet.size() > 0) {
                    List listtemp = new ArrayList();
                    for (int i = 0; i < signerList.size(); i++) {
                        Map maptemp = signerList.get(i);
                        String email = (String) maptemp.get("email");
                        if (email != null && !"".equals(email) && emailSet.contains(email)) {
                            listtemp.add(maptemp);
                        }
                    }
                    signerList = listtemp;
                }
            }

            // Create the main JSON map

            for (Map signerMap : signerList) {
                Map<String, Object> option = new HashMap<>();
                option.put("id", signerMap.get(nameFileId) + "<" + signerMap.get(emailFileId) + ">");
                option.put("text", signerMap.get(nameFileId) + "<" + signerMap.get(emailFileId) + ">");
                options.add(option);
            }


            // Create the main JSON map
            Map<String, Object> jsonMap = new HashMap<>();
            jsonMap.put("results", options);

            Map<String, Object> pagination = new HashMap<>();
            pagination.put("more", true);
            jsonMap.put("pagination", pagination);


            ResponseUtils.response(response, HttpStatus.OK, jsonMap);
//            daoDataMng.listRecord("ryjbzlb")
//            AttachDAO attdao = new AttachDAO(projectid);
//            DAOFileup fdao = new DAOFileup();
//            List listFile = fdao.parseToFile(inputfile);
////            if(listFile.size()==0) return lang.get("请选择授权文件！");
//            fdao.parseToFileName(inputfile);
//            File file = (File) listFile.get(0);


        } catch (Exception e) {
            throw new RuntimeException(e);
        }


    }

    public void studySearch(HttpServletRequest request, HttpServletResponse response) {
        try {


            String term = request.getParameter("q");
            String page = request.getParameter("page");

            String projectid = SessUtil.getSessInfo().getProjectid();

            List<Map<String, String>> LightpdfSignIntegrateList = DAOLightpdfSignIntegrate.listRule(projectid);
            Map<String, String> LightpdfSignIntegrateMap = LightpdfSignIntegrateList.get(0);
            String studyTableid = LightpdfSignIntegrateMap.get(DAOLightpdfSignIntegrate.studyTableid);
            String studyUserTableid = LightpdfSignIntegrateMap.get(DAOLightpdfSignIntegrate.studyUserTableid);
            String studyTableStudyFiledid = LightpdfSignIntegrateMap.get(DAOLightpdfSignIntegrate.studyTableStudyFiledid);
            List<Map<String, Object>> options = new ArrayList<>();
            DAODataMng daoDataMng = new DAODataMng(projectid);
            List<Map> signerList = daoDataMng.listRecord(studyTableid, "obj." + studyTableStudyFiledid + " like '%" + term + "%'", null, 10, StringUtils.isNotEmpty(page) ? Integer.parseInt(page) : 1);


            // Create the main JSON map

            for (Map signerMap : signerList) {
                Map<String, Object> option = new HashMap<>();
                option.put("id", signerMap.get("id"));
                option.put("text", signerMap.get(studyTableStudyFiledid));
                options.add(option);
            }


            // Create the main JSON map
            Map<String, Object> jsonMap = new HashMap<>();
            jsonMap.put("results", options);

            Map<String, Object> pagination = new HashMap<>();
            pagination.put("more", true);
            jsonMap.put("pagination", pagination);


            ResponseUtils.response(response, HttpStatus.OK, jsonMap);
//            daoDataMng.listRecord("ryjbzlb")
//            AttachDAO attdao = new AttachDAO(projectid);
//            DAOFileup fdao = new DAOFileup();
//            List listFile = fdao.parseToFile(inputfile);
////            if(listFile.size()==0) return lang.get("请选择授权文件！");
//            fdao.parseToFileName(inputfile);
//            File file = (File) listFile.get(0);


        } catch (Exception e) {
            throw new RuntimeException(e);
        }


    }

    public String[] wordToPdf(String signFile, String tableid, AttachDAO attachDAO, String projectId) throws Exception {

        String[] signFileArr = signFile.split("\\*");
        String signFilePathURI = attachDAO.getFilePathURI(signFileArr[1], tableid);
        String fileExtension = signFileArr[1].substring(signFileArr[1].lastIndexOf(".") + 1);
        String signFilePathFullURI = WebPath.getRootPath() + "/" + signFilePathURI;
        //配置xlsx转pdf
        if (fileExtension.equalsIgnoreCase("xlsx") || fileExtension.equalsIgnoreCase("xlsx")) {
            //请求远程xlsx转换接口，返回pdf文件路径
            String fileName=signFileArr[0].substring(0, signFileArr[0].lastIndexOf(".")) + ".xlsx";
            String uploadResult = LightpdfSignIntegrateUtil.uploadFile(sasOnlieApiPrefix+"transferXlsxToPdf", signFilePathFullURI, fileName);
            //判断接口是否成功
            Map uploadFileMsgMap = gson.fromJson(uploadResult, new TypeToken<Map<String, Object>>() {
            }.getType());
            Log.info("请求了pdf转换接口，返回结果为：" + uploadFileMsgMap.toString());
            if (StringUtils.equals(String.valueOf(uploadFileMsgMap.get("code")), "200.0")) {
                //下载文件流，保存到本地
                String folderPath = WebPath.getRootPath() + DAOFileup.tempfolder + "/eSign";
                String pdfName = signFileArr[0].substring(0, signFileArr[0].lastIndexOf(".")) + ".pdf";
                String downloadUrl=sasOnlieApiPrefix+"files/"+pdfName;
                LightpdfSignIntegrateUtil.downloadByNIO(downloadUrl,folderPath,pdfName);
                return new String[]{pdfName, folderPath+"/"+pdfName};
            }else{
                return null;
            }


        }else if (fileExtension.equalsIgnoreCase("doc") || fileExtension.equalsIgnoreCase("docx")) {
            DAOWordreport daoWordreport = new DAOWordreport(projectId);
            daoWordreport.convertDocToPdf(signFilePathFullURI, "2", "4");
        } else if (!fileExtension.equalsIgnoreCase("pdf")) {
            return null;
        }
        String fileName = signFileArr[0].substring(0, signFileArr[0].lastIndexOf(".")) + ".pdf";
        signFilePathFullURI = signFilePathFullURI.substring(0, signFilePathFullURI.lastIndexOf(".")) + ".pdf";

        return new String[]{fileName, signFilePathFullURI};
    }

    public void ajaxCancel(HttpServletRequest request, HttpServletResponse response) {
        try {

            String id = request.getParameter("id");
            String subtable = "{\"revokeReson\":{\n" +
                    "  \"id\": \"revokeReson\",\n" +
                    "  \"name\": \"废除原因\",\n" +
                    "  \"type\": \"clob\",\n" +
                    "  \"hidden\": false,\n" +
                    "  \"notNull\": true,\n" +
                    "  \"readonly\": false,\n" +
                    "  \"defaultValue\": \"\",\n" +
                    "  \"colSpan\": 1,\n" +
                    "  \"labelPos\": 2,\n" +
                    "  \"width\": \"400\",\n" +
                    "  \"height\": \"\",\n" +
                    "  \"textReadonly\": false,\n" +
                    "  \"fieldsVueCodes\": [],\n" +
                    "  \"dynFieldParams\": {\n" +
                    "    \"annotations\": {}\n" +
                    "  }\n" +
                    "}}";
            Gson json = new Gson();
            Map<String, Object> result = new HashMap();
            LinkedHashMap hashMap = (LinkedHashMap)json.fromJson(subtable, LinkedHashMap.class);
            result.put("subtable", hashMap);
            String value = "{\"revokeReson\":{\"hidden\":false,\"readonly\":false,\"defaultValue\":\"\",\"oldValue\":\"\",\"type\":\"clob\",\"showvalue\":\"\",\"value\":\"\"}}";
            result.put("value", json.fromJson(value, Map.class));
            List<Object> list = new ArrayList();
            Map<String, Object> canal = new HashMap();
            list.add(canal);
            canal.put("type", "onCanal");
            canal.put("name", "取消");
            Map<String, Object> ok = new HashMap();
            list.add(ok);
            ok.put("type", "onOk");
            ok.put("name", this.getLanguage().get("确定"));
            ok.put("url", "lightpdfSign.Revoke.do?esignInstanceId=" + request.getParameter("id"));
            result.put("button", list);
            response.getOutputStream().write(ApiResult.ok("OK",result).getBytes("UTF-8"));
            response.getOutputStream().close();
        } catch (Exception var11) {
            Log.error("", var11);
        }

    }
}
