//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package net.bioknow.cdtms.formMail;


import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import net.bioknow.cdtms.lightpdfSign.LightpdfSignIntegrateUtil;
import net.bioknow.mvc.RootAction;
import net.bioknow.mvc.codec.Md5Util;
import net.bioknow.mvc.tools.WebPath;
import net.bioknow.passport.datamng.DAOPPData;
import net.bioknow.passport.datamng.JDOUser;
import net.bioknow.passport.webvar.ServerType;
import net.bioknow.uap.dbcore.dbapi.DAODbApi;
import net.bioknow.uap.dbcore.schema.CNT_Schema;
import net.bioknow.uap.dbcore.schema.Schema;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.schemaplug.attach.AttachDAO;
import net.bioknow.uap.schemaplug.dtref.DtrefDAO;
import net.bioknow.uapplug.dbview.DAODbview;
import net.bioknow.uapplug.dbview.DbviewUtil;
import net.bioknow.webutil.fileup.DAOFileup;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import net.bioknow.webutil.tools.UUIDUtil;
import net.lingala.zip4j.core.ZipFile;
import net.lingala.zip4j.model.ZipParameters;
import net.lingala.zip4j.util.Zip4jConstants;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;


import static net.bioknow.cdtms.lightpdfSign.DAOLightpdfSignIntegrate.sasOnlieApiPrefix;
import static net.bioknow.uap.dbcore.dbmng.CNT_DbMng.userid;
import static org.apache.commons.lang3.StringUtils.contains;

public class ActionSendMail extends RootAction {


    public void getemail(HttpServletRequest request, HttpServletResponse response) {
        try {

            String projectId = SessUtil.getSessInfo().getProjectid();
            DAODataMng daoDataMng = new DAODataMng(projectId);
            String tableid;
            Map templateMap;
            String id = request.getParameter("id");
            String recordid = request.getParameter("recordid");
            String receiver = request.getParameter("receiver");
            String ccer = request.getParameter("ccer");
//            HttpServletRequest newRequest = (HttpServletRequest) request.getAttribute("javax.servlet.forward.request_uri");

            String isSign = request.getParameter("isSign");
            String signfuncid = request.getParameter("signfuncid");

            if (StringUtils.isEmpty(isSign)) {
                signfuncid = (String) request.getAttribute("signfuncid");
                isSign = (String) request.getAttribute("isSign");
            }


            if (StringUtils.isNotEmpty(id)) {
                templateMap = daoDataMng.getRecord("email_template", Long.valueOf(id));
                tableid = (String) templateMap.get("tableid");

            } else {

                tableid = request.getParameter("tableid");
                String where = request.getParameter("where");
                String whereE = "obj.tableid='" + tableid + "'";
                if (where != null && !"".equals(where)) {
                    whereE = whereE + " and (" + where + ")";
                }


                List<Map> templateList = daoDataMng.listRecord("email_template", whereE, "", 100);

                if (CollectionUtils.isEmpty(templateList)) {

                    response.setContentType("application/json;charset=utf-8");
                    response.getOutputStream().write("配置错误".getBytes("utf-8"));
                    response.getOutputStream().close();
                }
                templateMap = (Map) templateList.get(0);
            }


            DtrefDAO dtrefDAO = new DtrefDAO(projectId);
            String RefField = dtrefDAO.getRefField("xsht", tableid);
            String email_template_content = (String) templateMap.get("content");
            String reply = (String) templateMap.get("reply");
            String email_template_subject = (String) templateMap.get("subject");
            String email_from = (String) templateMap.get("from");
            String email_template_receiver_role = "," + (String) templateMap.get("receiver_role") + ",";
            String email_template_cc_role = "," + (String) templateMap.get("cc_role") + ",";
            String files = (String) templateMap.get("files");
            String is_files_compress_encrypt = (String) templateMap.get("is_files_compress_encrypt");
            email_template_content = email_template_content.replaceAll("----CLOBHTMLSPLITOR----CLOBHTMLSPLITOR----", "");
            Map valueMap = daoDataMng.getRecord(tableid, Long.parseLong(recordid));


            Long studyid = (Long) valueMap.get(RefField);
            email_template_receiver_role = replaceTemplate(email_template_receiver_role, projectId, tableid, "", valueMap, "2", "zh", "0");


            String content = replaceTemplate(email_template_content, projectId, tableid, "", valueMap, is_files_compress_encrypt, "zh", "0");
            String subject = replaceTemplate(email_template_subject, projectId, tableid, "", valueMap, "2", "zh", "0");
            List<String> listFN = new ArrayList();
            String fileCheckbox = "";
            if (StringUtils.isNotEmpty(files)) {
                File[] fA = getFilesFromTemplate(files, projectId, tableid, valueMap, listFN, "0");
                if (fA != null && fA.length > 0) {
                    for (int i = 0; i < fA.length; ++i) {
                        String fileName = (String) listFN.get(i);
                        String fileKey = fA[i].getName();
                        fileCheckbox += "<input type=\"checkbox\" name=\"files\"  value=\"" + fileKey + "||" + fileName.replace(",", "$") + "\" checked>" + fileName.split("\\|\\|")[0] + "<br>";
                    }
                }
            }


            String receiverFilter = "(studyid='" + studyid + "' or studyid is null)";

            String receiverFilterAddSql = (String) templateMap.get("contact_filter_where");

            if (StringUtils.isNotEmpty(receiverFilterAddSql)) {
                receiverFilter+=" and ("+receiverFilterAddSql+")";
            }


            DAODbview dbview = new DAODbview(projectId);
            int index = dbview.getRuleIndexByName("项目参与人");
            List listC = DbviewUtil.getDataList(projectId, index + "", receiverFilter, "", 999, 1);
            String rec = "";
            String cc = "";
            if (listC != null && listC.size() > 0) {
                for (int i = 0; i < listC.size(); ++i) {
                    Map map = (Map) listC.get(i);
                    String name = (String) map.get("name");
                    String email = (String) map.get("email");
                    String role = (String) map.get("role");
                    String recselected = (String) map.get("recselected");
                    String ccselected = (String) map.get("ccselected");
                    rec = rec + "{name:'" + name + "',value:'" + email + "'";

                    if ("1".equals(recselected) || email_template_receiver_role.contains("," + role + ",") || email_template_receiver_role.contains("," + email + ",") ||
                            contains("," + receiver + ",", "," + email + ",")) {
                        rec = rec + ",selected: true";
                    }

                    rec = rec + "},";
                    cc = cc + "{name:'" + name + "',value:'" + email + "'";
                    if ("1".equals(ccselected) || email_template_cc_role.contains("," + role + ",") || email_template_cc_role.contains("," + email + ",") || contains("," + ccer + ",", "," + email + ",")) {
                        cc = cc + ",selected: true";
                    }

                    cc = cc + "},";
                }
            }

            request.setAttribute("tableid", tableid);
            request.setAttribute("isCompress", is_files_compress_encrypt);
            request.setAttribute("recordid", recordid);
            request.setAttribute("content", content);
            request.setAttribute("subject", subject);
            request.setAttribute("reply", reply);
            request.setAttribute("email_from", email_from);
            request.setAttribute("fileCheckbox", fileCheckbox);
            request.setAttribute("studyid", studyid);
            request.setAttribute("listRec", rec);
            request.setAttribute("listCc", cc);
            request.setAttribute("isSign", isSign);
            request.setAttribute("signfuncid", signfuncid);
            request.setAttribute("email_template_content", email_template_content);
            this.forward(request, response, "email");
        } catch (Exception var40) {
            Log.error(var40.getMessage(), var40);
        }

    }

    public void ajaxmenu(HttpServletRequest request, HttpServletResponse response) {


        try {
         /*   String tableid = request.getParameter("tableid");
            String id = request.getParameter("id");

            String projectId = SessUtil.getSessInfo().getProjectid();
            DAODataMng daoDataMng = new DAODataMng(projectId);
            List<Map> engineList = daoDataMng.listRecord("email_template", "obj.tableid='" + tableid + "'", null, 100);
            ArrayList<Map> currEngineList = new ArrayList<>();
            for (Map engineMap : engineList) {
                String showWhere = (String) engineMap.get("where");
                if (org.apache.commons.lang.StringUtils.isEmpty(showWhere)) {
                    showWhere = "1=1";
                }
                int ActiveEngineCount = daoDataMng.count(tableid, "obj.id=" + id + " and (" + showWhere + ")");
                if (ActiveEngineCount == 1) {
                    currEngineList.add(engineMap);
                }
            }
//			if(currEngineList.size()==1){
//				this.redirectByUri(request, response,"actionsendmail.getemail.do?id="+currEngineList.get(0).get("id")+"&recordid="+fpb.getRecordid());
//			}else {*/
//            request.setAttribute("listr", currEngineList);
            request.setAttribute("uuid", UUIDUtil.get());
            this.forward(request, response, "ajaxmenu");
//
        } catch (Exception e) {
            Log.error("", e);
        }

    }


    public void ajaxmenuVue(HttpServletRequest request, HttpServletResponse response) {


        try {
            String tableid = request.getParameter("tableid");
            String id = request.getParameter("id");

            String projectId = SessUtil.getSessInfo().getProjectid();
            DAODataMng daoDataMng = new DAODataMng(projectId);
            List<Map> engineList = daoDataMng.listRecord("email_template", "obj.tableid='" + tableid + "'", null, 100);
            ArrayList<Map> currEngineList = new ArrayList<>();
            for (Map engineMap : engineList) {
                String showWhere = (String) engineMap.get("where");
                if (org.apache.commons.lang.StringUtils.isEmpty(showWhere)) {
                    showWhere = "1=1";
                }
                int ActiveEngineCount = daoDataMng.count(tableid, "obj.id=" + id + " and (" + showWhere + ")");
                if (ActiveEngineCount == 1) {
                    currEngineList.add(engineMap);
                }
            }
//			if(currEngineList.size()==1){
//				this.redirectByUri(request, response,"actionsendmail.getemail.do?id="+currEngineList.get(0).get("id")+"&recordid="+fpb.getRecordid());
//			}else {
            request.setAttribute("listr", currEngineList);
            request.setAttribute("uuid", UUIDUtil.get());
            request.setAttribute("id", id);
            this.forward(request, response, "ajaxmenuVue");
//
        } catch (Exception e) {
            Log.error("", e);
        }

    }

    private static String calculateMD5(String inputFile) throws IOException, NoSuchAlgorithmException {
        try (InputStream is = Files.newInputStream(new File(inputFile).toPath())) {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] buffer = new byte[8192];
            int read;
            while ((read = is.read(buffer)) > 0) {
                md.update(buffer, 0, read);
            }
            byte[] md5Bytes = md.digest();
            StringBuilder md5 = new StringBuilder();
            for (byte md5Byte : md5Bytes) {
                md5.append(Integer.toString((md5Byte & 0xff) + 0x100, 16).substring(1));
            }
            return md5.toString();
        }
    }

    public void sendMail(HttpServletRequest request, HttpServletResponse response) {
        try {
            String projectId = SessUtil.getSessInfo().getProjectid();
            DAODataMng dmdao = new DAODataMng(projectId);
            String receiver = request.getParameter("receiver");
            String tableid = request.getParameter("tableid");
            String reply = request.getParameter("reply");
            String emailSender = request.getParameter("email_from");
            String recordid = request.getParameter("recordid");
            String cc = request.getParameter("cc");
            String subject = request.getParameter("subject");
            String content = request.getParameter("content");
            String fileSelected = request.getParameter("fileSelected");
            String isCompress = request.getParameter("isCompress");
            String studyid = request.getParameter("studyid");


            File[] attachFiles = null;
            String[] attachFileNames = null;

            File[] passwordFiles = null;
            String[] passwordNames = null;
            if (StringUtils.isNotEmpty(fileSelected)) {
                AttachDAO attachDAO = new AttachDAO(projectId);
                String[] fileSelectedArr = fileSelected.split(",");
                if (!ArrayUtils.isEmpty(fileSelectedArr)) {
                    if (StringUtils.equals(isCompress, "1")) {
                        ArrayList<File> Zipfiles = new ArrayList<File>();
                        for (int i = 0; i < fileSelectedArr.length; ++i) {
                            String[] fileInfoArr = fileSelectedArr[i].split("\\|\\|");
                            File file = attachDAO.getFile(fileInfoArr[0], tableid);




                            String fileName = fileInfoArr[1].replace("$", ",");
                            String filePath = WebPath.getRootPath() + DAOFileup.tempfolder + "/" + UUIDUtil.get() + "/" + fileName;
                            File targetFile = new File(filePath);
                            FileUtils.copyFile(file, targetFile);
                            Zipfiles.add(targetFile);
                        }
                        String password = UUIDUtil.get().substring(0, 6);
                        ZipParameters zipParameters = zipPasswordSet(password);
                        String zipfilePath = WebPath.getRootPath() + DAOFileup.tempfolder + "/" + UUIDUtil.get() + "/附件.zip";
                        File zipfile = new File(zipfilePath);
                        zipfile.getParentFile().mkdirs();
                        ZipFile zipFile = new ZipFile(zipfilePath);


                        zipFile.addFiles(Zipfiles, zipParameters);
                        attachFiles = new File[]{zipFile.getFile()};
                        attachFileNames = new String[]{subject+".zip"};
                        String passwordFilePath = WebPath.getRootPath() + DAOFileup.tempfolder + "/" + UUIDUtil.get() + "/" + "password.txt";
                        File passwordfile = new File(passwordFilePath);
                        passwordfile.getParentFile().mkdirs();
//                        PrintWriter passwordwriter = new PrintWriter(passwordfile);
//                        passwordwriter.println(password);
//                        passwordwriter.close();

                        FileUtils.write(passwordfile, password, "UTF-8");
                        passwordFiles = new File[]{passwordfile};
                        passwordNames = new String[]{"password.txt"};

                    } else {
                        attachFiles = new File[fileSelectedArr.length];
                        attachFileNames = new String[fileSelectedArr.length];
                        Boolean isDuplicate = false;

                        // 创建一个Map来存储文件名前缀和对应的文件类型
                        Map<String, Set<String>> fileNameMap = new HashMap<>();

                        // 首先遍历所有文件，提取文件名前缀和扩展名
                        for (int i = 0; i < fileSelectedArr.length; ++i) {
                            String fileInfo = fileSelectedArr[i].toString();

                            // 解析文件信息
                            String[] parts = fileInfo.split("\\|\\|");
                            if (parts.length >= 2) {
                                String fileName = parts[1];

                                // 检查文件名是否包含扩展名
                                if (fileName.contains(".")) {
                                    // 提取文件名前缀（不含扩展名）
                                    String fileNamePrefix = fileName.substring(0, fileName.lastIndexOf("."));

                                    // 提取扩展名
                                    String extension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();

                                    // 将扩展名添加到对应文件名前缀的集合中
                                    fileNameMap.computeIfAbsent(fileNamePrefix, k -> new HashSet<>()).add(extension);
                                }
                            }
                        }

                        // 现在检查每个xlsx文件是否有对应的pdf文件
                        for (int i = 0; i < fileSelectedArr.length; ++i) {
                            String fileInfo = fileSelectedArr[i].toString();
                            String[] parts = fileInfo.split("\\|\\|");

                            if (parts.length >= 2) {
                                String fileName = parts[1];

                                if (fileName.toLowerCase().endsWith(".xlsx")) {
                                    String fileNamePrefix = fileName.substring(0, fileName.lastIndexOf("."));

                                    // 检查这个文件名前缀是否有对应的pdf文件
                                    Set<String> extensions = fileNameMap.get(fileNamePrefix);
                                    boolean hasPdfVersion = extensions != null && extensions.contains("pdf");

                                    Log.info("文件 [" + fileName + "] " + (hasPdfVersion ? "有" : "没有") + "对应的PDF版本");

                                    // 如果有对应的PDF版本，可以在这里跳过处理xlsx文件
                                    if (hasPdfVersion) {
                                        // 跳过处理xlsx文件的代码
                                        Log.info("跳过处理xlsx文件: " + fileName);
                                        isDuplicate=true;
                                    }

                                    // 处理没有对应PDF版本的xlsx文件
                                    // ...原有的处理逻辑...
                                }
                            }
                        }










                        for (int i = 0; i < fileSelectedArr.length; ++i) {
                            String[] fileInfoArr = fileSelectedArr[i].split("\\|\\|");
                            if (fileInfoArr.length == 3&&!fileInfoArr[2].toString().isEmpty()) {
                                attachFiles[i] = attachDAO.getFile(fileInfoArr[0], fileInfoArr[2]);
                            } else {
                                attachFiles[i] = attachDAO.getFile(fileInfoArr[0], tableid);
                                //拿到fid
                                String fid=fileInfoArr[fileInfoArr.length-1];
                                //转pdf
                                //附件判断如果是xlsx文件，转为pdf
                                if( attachFiles[i].getName().substring( attachFiles[i].getName().lastIndexOf("."), attachFiles[i].getName().length() ).equals(".xlsx")&&!isDuplicate){
                                        Log.info("发送邮件的附件列表附件不存在转换过的xlsx文件的pdf,执行pdf转换逻辑：" + attachFiles[i].getName() );
                                        //请求远程xlsx转换接口，返回pdf文件路径
                                        String fileName= attachFiles[i].getName();
                                        String uploadResult = LightpdfSignIntegrateUtil.uploadFile(sasOnlieApiPrefix+"transferXlsxToPdf",  attachFiles[i].getAbsolutePath().toString(), fileName);
                                        //判断接口是否成功
                                        Gson gson = new Gson();
                                        Map uploadFileMsgMap = gson.fromJson(uploadResult, new TypeToken<Map<String, Object>>() {
                                        }.getType());
                                        Log.info("请求了pdf转换接口，返回结果为：" + uploadFileMsgMap.toString());
                                        if (StringUtils.equals(String.valueOf(uploadFileMsgMap.get("code")), "200.0")) {
                                            //下载文件流，保存到本地
                                            String folderPath = WebPath.getRootPath() + DAOFileup.tempfolder + "/eSign";
                                            File folder=new File(folderPath);
                                            if(!folder.exists()){
                                                folder.mkdir();
                                            }
                                            String pdfName = fileName.substring(0,fileName.lastIndexOf(".")) + ".pdf";
                                            String encodePdfName= URLEncoder.encode(pdfName, StandardCharsets.UTF_8.toString());
                                            String downloadUrl=sasOnlieApiPrefix+"files/"+encodePdfName;
                                            LightpdfSignIntegrateUtil.downloadByNIO(downloadUrl,folderPath,pdfName);
                                            //放回原来的附件
                                            File pdfFile= new File(folderPath+"/"+pdfName);
                                            //存pdf
                                            String fileUuid = attachDAO.saveFile(pdfFile, tableid);
                                            Map record = dmdao.getRecord(tableid, Long.valueOf(recordid));
                                            //追加pdf记录，修复pdf后缀名问题
                                            Log.info("邮件发送附件回填的参数是：" +fileInfoArr[1]);
                                            record.put(fid,record.get(fid)+ fileInfoArr[1].substring(0, fileInfoArr[1].lastIndexOf("."))+".pdf"+"*"+fileUuid+"|");
                                            dmdao.save(tableid,record);
                                        }
                                }
                            }
                            attachFileNames[i] = fileSelectedArr[i].split("\\|\\|")[1].replace("$", ",");
                        }
                    }
                }
            }

            Map sendLogToSave = new HashMap();
            sendLogToSave.put("tableid", tableid);
            sendLogToSave.put("recordid", Long.parseLong(recordid));
            sendLogToSave.put("receiver", receiver);
            sendLogToSave.put("subject", subject);
            sendLogToSave.put("file_name", ArrayUtils.isNotEmpty(attachFileNames) ? Arrays.toString(attachFileNames) : "");
            sendLogToSave.put("cc", cc);
            sendLogToSave.put("content", content);
            sendLogToSave.put("study_id", Long.parseLong(studyid));
            DAOTransemail daoTransemail = new DAOTransemail(projectId);
            String loginid = !"supersa".equals(SessUtil.getSessInfo().getUserloginid()) && !"sa".equals(SessUtil.getSessInfo().getUserloginid()) && !"public".equals(reply) ? SessUtil.getSessInfo().getUserloginid() : "";
            dmdao.save("email_send_log", sendLogToSave);


            String isSign = request.getParameter("isSign");
            String needSigin = request.getParameter("needSigin");

            if (StringUtils.equals("1", isSign) && StringUtils.equals("1", needSigin)) {
                this.forwardByUri(request, response, "LightpdfSignIntergrate.eSignRegister.do");
                return;
            }

            if (StringUtils.equals("2", isSign)) {
                this.forwardByUri(request, response, "extdatabind.sendMail.do");
                return;
            }


            String sendMailResult = daoTransemail.sendmail(emailSender, subject, content, receiver, cc, loginid, attachFiles, attachFileNames);

            if (StringUtils.equals(isCompress, "1")) {
                DtrefDAO dtrefDAO = new DtrefDAO(projectId);
                DAODataMng daoDataMng = new DAODataMng(projectId);
                String studyCode = "";
                String RefStudyField = dtrefDAO.getRefField("xsht", tableid);
                if (StringUtils.isNotEmpty(RefStudyField)) {
                    Map DataMap = daoDataMng.getRecord(tableid, Long.valueOf(recordid));
                    Long RefStudyId = (Long)DataMap.get(RefStudyField);
                    studyCode= (String) daoDataMng.getRecord("xsht", RefStudyId).get("studyid");

                }
                String sendMailResult2 = daoTransemail.sendmail(emailSender, subject+"_密码", "附件为文件密码，请查收。", receiver, cc, loginid, passwordFiles, passwordNames);

            }

            if (StringUtils.equals(sendMailResult, "ERR")) {
                response.getOutputStream().write("发送失败，请检查您的收件人邮箱是否准确或者联系管理员".getBytes(StandardCharsets.UTF_8));
                response.getOutputStream().close();

                return;

            }


            response.getOutputStream().write("发送成功".getBytes(StandardCharsets.UTF_8));
            response.getOutputStream().close();

        } catch (Exception var24) {
            Log.error(var24.getMessage(), var24);
            var24.printStackTrace();
        }

    }

    public static ZipParameters zipPasswordSet(String password) {
        //设置压缩文件参数
        ZipParameters parameters = new ZipParameters();
        //设置压缩方法
        parameters.setCompressionMethod(Zip4jConstants.COMP_DEFLATE);
        //设置压缩级别
        parameters.setCompressionLevel(Zip4jConstants.DEFLATE_LEVEL_NORMAL);
        //设置压缩文件是否加密
        parameters.setEncryptFiles(true);
        //设置aes加密强度
        parameters.setAesKeyStrength(Zip4jConstants.AES_STRENGTH_256);
        //设置加密方法
        parameters.setEncryptionMethod(Zip4jConstants.ENC_METHOD_AES);
        //设置密码
        parameters.setPassword(password.toCharArray());
        return parameters;
    }


    private static File[] getFilesFromTemplate(String template, String projectId, String tableid, Map mapV, List<String> listFN, String debug) {
        try {
            DAODbApi dadao = new DAODbApi(projectId);
            AttachDAO attdao = new AttachDAO(projectId);
            List<File> listF = new ArrayList<File>();
            while (true) {
                int p = template.indexOf("{");
                if (p < 0) break;
                int p2 = template.indexOf("}", p);
                String fid = template.substring(p + 1, p2);

                if (fid.indexOf(",") > 0) {//tid,fid,where  where中用 [fid]表示替换本记录字段值
                    String[] fidA = fid.split(",");
                    if (fidA.length == 3) {
                        String tidLink = fidA[0];
                        String fidLink = fidA[1];
                        String whereLink = fidA[2];
                        while (true) {
                            int pp = whereLink.indexOf("[");
                            if (pp < 0) break;
                            int pp2 = whereLink.indexOf("]", pp);
                            if (pp2 < 0) break;
                            String fidInner = whereLink.substring(pp + 1, pp2);
                            whereLink = whereLink.substring(0, pp) + mapV.get(fidInner) + whereLink.substring(pp2 + 1);
                        }
                        DAODataMng daoDataMng = new DAODataMng(projectId);

                        List listInner = daoDataMng.listRecord(tidLink, whereLink, "obj.id desc", 1);
                        if (listInner.size() > 0) {
                            Map mapVLink = (Map) listInner.get(0);
                            Map mapF = dadao.getMapFieldCopy(tidLink, fidLink);

                            if (mapF != null) {
                                String type = (String) mapF.get(CNT_Schema.type);
                                if (type.equals("attach")) {
                                    String value = (String) mapVLink.get(fidLink);
                                    if (value != null && !value.equals("")) {
                                        try {
                                            File[] fA = attdao.getFiles(value, tidLink);
                                            String[] fNameA = attdao.getFileNames(value, tidLink);
                                            for (int i = 0; i < fA.length; i++) {
                                                listF.add(fA[i]);
                                                listFN.add(fNameA[i] + "||" + tidLink+"||" + fidLink);
                                                Log.info("--------处理之后的附件value是:"+fNameA[i] + "||" + tidLink+"||" + fidLink);
                                            }
                                        } catch (Exception e) {
                                            Log.error("", e);
                                        }
                                    }
                                }
                            }
                        }
                    }
                } else {
                    Map mapF = dadao.getMapFieldCopy(tableid, fid);
                    if (mapF != null) {
                        String type = (String) mapF.get(CNT_Schema.type);
                        if (type.equals("attach")) {
                            String value = (String) mapV.get(fid);
                            if (value != null && !value.equals("")) {
                                try {
                                    File[] fA = attdao.getFiles(value, tableid);
                                    String[] fNameA = attdao.getFileNames(value, tableid);
                                    for (int i = 0; i < fA.length; i++) {
                                        if (debug != null && debug.equals("1")) {
                                            Log.info("Attachment:" + fA[i].getAbsolutePath());
                                        }
                                        listF.add(fA[i]);
                                      //  listFN.add(fNameA[i]);
                                       listFN.add(fNameA[i] +"||"+"||"+fid);
                                        Log.info("--------处理之后的附件value是:"+fNameA[i] +"||"+"||"+fid);
                                    }
                                } catch (Exception e) {
                                    Log.error("", e);
                                }
                            }
                        }
                    }
                }
                template = template.substring(0, p) + template.substring(p2 + 1);


            }
            File[] fA = new File[listF.size()];
            for (int i = 0; i < listF.size(); i++) {
                fA[i] = listF.get(i);
            }
            return fA;
        } catch (Exception e) {
            Log.error("",e);
        }
        return null;
    }



    private static String replaceTemplate(String template,String projectId, String tableid,
                                          String username,Map mapV,String attachtype,String language,String debug) throws Exception{
        DAODbApi dadao = new DAODbApi(projectId);
        DAODataMng dmdao = new DAODataMng(projectId);
//        JDOUser user = new DAOPPData(projectId).getUserById(Long.valueOf(SessUtil.getSessInfo().getUserid()));



        while(true){
            int p = template.indexOf("{");
            if(p<0)break;
            int p2 = template.indexOf("}",p);
            String fid = template.substring(p+1,p2);
            if(debug!=null && debug.equals("1")){
                Log.info("{"+fid+"}:"+fid);
            }
            String v = "";
            if(fid.startsWith("$user")){
                String[] split2 = fid.split("\\.");
                if(split2.length==1){
                    v = (String) SessUtil.getSessInfo().getUser().get("username");
                }else if(split2.length==2){
                    v = String.valueOf(SessUtil.getSessInfo().getUser().get(split2[1]));
                }
            }else if(fid.startsWith("$date")){
                String[] split = fid.split("\\|");
                if(split.length==1){
                    v = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
                }else if(split.length==2){
                    v = new SimpleDateFormat(split[1]).format(new Date());
                }else if(split.length>2){
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat(split[1]);
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(new Date());
                    String string = split[2];
                    int unit = Calendar.DAY_OF_YEAR;
                    if(string.startsWith("y")) unit = Calendar.YEAR;
                    if(string.startsWith("m")) unit = Calendar.MONTH;
                    String substring = string.substring(1, 2);
                    String substring1 = string.substring(2);
                    int num = Integer.parseInt(substring1);
                    if(StringUtils.equals("-",substring)) num = -num;
                    calendar.add(unit,num);
                    v = simpleDateFormat.format(calendar.getTime());
                }

            }else if(fid.equalsIgnoreCase("_username")){
                v = username;
            }else if(fid.equalsIgnoreCase("_loginpage")){
                String addr = ServerType.getCurrentAddr(projectId);
                v = addr +"/"+projectId;
            }else if(fid.equalsIgnoreCase("_serveraddr")){
                v = ServerType.getCurrentAddr(projectId);
            }else if(fid.equalsIgnoreCase("_projectid")){
                v = projectId;
            }else if(fid.equalsIgnoreCase("id")){
                v = String.valueOf(mapV.get(CNT_Schema.id));
            }else if(fid.substring(0,1).equals("$")){
                String fidTemp = fid.substring(1);
                v = (mapV.get(fidTemp)==null?"":String.valueOf(mapV.get(fidTemp)));
            }else if(fid.indexOf(",")>0){//tid,fid,where  where中用 [fid]表示替换本记录字段值
                String[] fidA = fid.split(",");
                if(fidA.length==3){
                    String tidLink = fidA[0];
                    String fidLink = fidA[1];
                    String whereLink = fidA[2];
                    while(true){
                        String v2="";
                        int pp = whereLink.indexOf("[");
                        if(pp<0) break;
                        int pp2 = whereLink.indexOf("]",pp);
                        if(pp2<0)break;
                        String fidInner = whereLink.substring(pp+1,pp2);
                        if(fidInner.startsWith("$user")){
                            String[] split2 = fidInner.split("\\.");
                            if(split2.length==1){
                                v2 = (String) SessUtil.getSessInfo().getUser().get("username");
                            }else if(split2.length==2){
                                v2 = String.valueOf(SessUtil.getSessInfo().getUser().get(split2[1]));
                            }
                        }else{
                            v2= String.valueOf(mapV.get(fidInner));
                        }
                        whereLink = whereLink.substring(0,pp)+v2+whereLink.substring(pp2+1);
                    }
                    if(debug!=null && debug.equals("1")){
                        Log.info("whereLink:"+tidLink+" "+whereLink);
                    }
                    List listInner = dmdao.listRecord(tidLink, whereLink, "obj.id desc", 1);
                    if(debug!=null && debug.equals("1")){
                        Log.info(" find:"+listInner.size());
                    }
                    if(listInner.size()>0){
                        Map mapVLink = (Map)listInner.get(0);
                        if(debug!=null && debug.equals("1")){
                            Log.info(" mapVLink:"+mapVLink);
                        }
                        if(fidLink.equals("id")){
                            v = String.valueOf(mapVLink.get("id"));
                        }else{
                            Schema schema = dadao.getFieldType(tidLink, fidLink);
                            Map mapF = dadao.getMapFieldCopy(tidLink, fidLink);
                            v = schema.formatToOutput(tidLink, mapF, mapVLink);
                        }
                    }
                    if(debug!=null && debug.equals("1")){
                        Log.info(" value:"+v);
                    }
                }
            }else if(fid.indexOf(".")>0){
                String[] fidA = fid.split("\\.");
                String fidRef = fidA[0];
                String fidParent = fidA[1];
                Long ridParent = (Long)mapV.get(fidRef);
                if(ridParent!=null){
                    Map mapFRef = dadao.getMapFieldCopy(tableid, fidRef);
                    String tidParent = (String)mapFRef.get(CNT_Schema.refclassname);
                    Map mapVRef = dmdao.getRecord(tidParent, ridParent);
                    Schema schema = dadao.getFieldType(tidParent, fidParent);
                    Map mapFParent = dadao.getMapFieldCopy(tidParent, fidParent);
                    v = schema.formatToOutput(tidParent, mapFParent, mapVRef);
                }else{
                    v = "";
                }
            }else{

                Schema schema = dadao.getFieldType(tableid, fid);
                Map mapF = dadao.getMapFieldCopy(tableid, fid);
                if(mapF==null) {
                    v = projectId + " "+tableid+"."+fid+" is not exist!";
                }else {
                    String type = (String)mapF.get(CNT_Schema.type);
                    if(type.equals("attach")){
                        String value = (String)mapV.get(fid);
                        if(value!=null && !value.equals("")){
                            if(attachtype!=null && attachtype.equals("1")) {
                                AttachDAO attachDAO = new AttachDAO(projectId);
                                File[] files = attachDAO.getFiles(value, tableid);
                                String[] fileNames = attachDAO.getFileNames(value, tableid);
                                for (int i = 0; i < files.length; i++) {
                                    v += fileNames[i]+"<br/>MD5:"+calculateMD5(files[i].getPath())+";<br/>";
                                }
                            }else {
                                v = schema.formatToOutput(tableid, mapF, mapV);
                            }
                        }
                    }else {
                        v = schema.formatToOutput(tableid, mapF, mapV);
                    }

                }
            }
            if(debug!=null && debug.equals("1")){
                Log.info("{"+fid+"}:"+v);
            }
            v = DAODataMng.whereStrReplace(v);
            template = template.substring(0,p)+v+template.substring(p2+1);
        }
        return template;
    }


}
