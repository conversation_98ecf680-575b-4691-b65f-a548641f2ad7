package net.bioknow.cdtms.lightpdfSign;

import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.webplug.transemail.ThreadSendmail;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.lang.StringUtils;

import java.text.SimpleDateFormat;
import java.util.*;

public class TimerUrgingSigners extends TimerTask {

    @Override
    public void run() {
        try {
            Log.info("TimerUrgingSigners start");
            // Get all active projects
           String projectId = "cdtmsen_val";
//            String projectId = "clinicalinte";
           processProject(projectId);
        } catch (Exception e) {
            Log.error("TimerUrgingSigners error", e);
        }
    }

    private void processProject(String projectId) {
        try {
            DAODataMng daoDataMng = new DAODataMng(projectId);

            // Get configuration
            List<Map<String, String>> configList = DAOLightpdfSignIntegrate.listRule(projectId, "1");
            if (configList == null || configList.isEmpty()) {
                return;
            }

            Map<String, String> config = configList.get(0);
            String CDTMSUrl = config.get(DAOLightpdfSignIntegrate.CDTMSUrl);

            // Find pending signers (status=0 means not signed yet)
            List<Map> pendingSigners = daoDataMng.listRecord(
                    "esign_signer",
                    "obj.status=0",
                    "obj.createtime desc",
                    1000
            );

            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date now = new Date();

            for (Map signerMap : pendingSigners) {
                Long esignInstanceId = (Long) signerMap.get("esign_instance_id");
                Map instanceMap = daoDataMng.getRecord("esign_instance", esignInstanceId);

                // Skip if instance doesn't exist or is not active
                if (instanceMap == null) {
                    Log.info("Instance not found for ID: " + esignInstanceId);
                    continue;
                }
                // Skip if instance is not active
                if (!"1".equals(String.valueOf(instanceMap.get("active")))) {
                    continue;
                }

                // Check if expiration date is approaching (within 24 hours)
                Date expireDate = (Date) instanceMap.get("sign_expire_data");
                if (expireDate == null) {
                    continue;
                }

                long timeRemaining = expireDate.getTime() - now.getTime();
                // Only send reminder if less than 24 hours remaining
                if (timeRemaining > 0 && timeRemaining <  24 * 60 * 60 * 1000) {
                    sendUrgingEmail(projectId, signerMap, instanceMap, CDTMSUrl, dateFormat.format(expireDate));
                }
            }
        } catch (Exception e) {
            Log.error("Error processing project " + projectId, e);
        }
    }

    private void sendUrgingEmail(String projectId, Map signerMap, Map instanceMap, String CDTMSUrl, String expireTime) {
        try {
            String email = (String) signerMap.get("user_code");
            String language = (String) instanceMap.get("language");
            String fileName = (String) instanceMap.get("file_name");
            String initiator = (String) instanceMap.get("initiator");
            String backProcessId = (String) instanceMap.get("sign_flow_id");
            String signerName = signerMap.getOrDefault("name", "").toString();

            String content;
            String subject = "《" + fileName.replace(".pdf", "") + "》 need to be signed!";

            if (StringUtils.equals(language, "en")) {
                content =   "Hello " + signerName +
                        "<br>You have a document \"" + fileName.replace(".pdf", "") + "\" need to be signed before " + expireTime +
                        "<br>" +
                        "Signature CAPTCHA is " + signerMap.get("verification_code")  +
                        "<br><br><a href=\"" + CDTMSUrl + "/lightpdfSign.checkIsLogin.do?system=" + projectId + "&task_id=" + backProcessId + "&email=" +
                        new String(Base64.getEncoder().encode(email.getBytes())) + "\" title=\"go to sign\">Go to sign</a><br><br><br>" +
                        "Initiator: " + initiator +
                        "<br><span style=\"font-size='9px'\">This email is sent by the system, please do not reply</span>"
                        + "<br><br><a href=\"https://cdtms.hengrui.com/webutil/js/pdfjs/web/viewer.show.do?file=/%E7%BA%BF%E4%B8%8A%E7%AD%BE%E5%90%8D%E5%8A%9F%E8%83%BD%E6%93%8D%E4%BD%9C%E6%8C%87%E5%8D%97_20231101.pdf&amp;unloadstr=\" target=\"_blank\" style=\"font-weight: bold;color: red;\">Signature operation guide</a>";




            } else {
                content = "您好，" + signerName + "<br> " +
                        "您有一份文件 《" + fileName.replace(".pdf", "") + "》 需要签署，请于" + expireTime + "前完成。<br>" +
                        "签字验证码:" + signerMap.get("verification_code") +
                        "<br><br><a href=\"" + CDTMSUrl + "/lightpdfSign.checkIsLogin.do?system=" + projectId + "&task_id=" + backProcessId + "&email=" +
                        new String(Base64.getEncoder().encode(email.getBytes())) + "&language=" + language + "\" title=\"去签署\">去签署</a><br><br><br>" +
                        "签字发起人：" + initiator +
                        "<br><span style=\"font-size='9px'\">该邮件为系统发出，请勿回复</span>"
                        + "<br><br><a href=\"https://cdtms.hengrui.com/webutil/js/pdfjs/web/viewer.show.do?file=/%E7%BA%BF%E4%B8%8A%E7%AD%BE%E5%90%8D%E5%8A%9F%E8%83%BD%E6%93%8D%E4%BD%9C%E6%8C%87%E5%8D%97_20231101.pdf&amp;unloadstr=\" target=\"_blank\" style=\"font-weight: bold;color: red;\">在线签署操作指南</a><br><br>";

            }

            ThreadSendmail.addTask(projectId, subject, content, email);

        } catch (Exception e) {
            Log.error("Error sending urging email", e);
        }
    }
}