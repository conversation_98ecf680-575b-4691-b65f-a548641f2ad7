package net.bioknow.cdtms.lightpdfSign;

import net.bioknow.dbplug.batchadd.CNT_Batchadd;
import net.bioknow.dbplug.batchadd.DAOBatchaddCFG;
import net.bioknow.mvc.tools.Language;
import net.bioknow.passport.power.PowerUtil;
import net.bioknow.uap.dbcore.dbapi.DAODbApi;
import net.bioknow.uap.dbcore.schema.CNT_Schema;
import net.bioknow.uap.dbdatamng.DAODataMng;
import net.bioknow.uap.dbdatamng.function.DTTableFuncAction;
import net.bioknow.uap.dbdatamng.function.FuncInfoBean;
import net.bioknow.uap.dbdatamng.function.FuncParamBean;
import net.bioknow.webutil.session.SessUtil;
import net.bioknow.webutil.tools.Log;
import net.bioknow.webutil.tools.UUIDUtil;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class DTTFActionSign extends DTTableFuncAction {

    public boolean canUse(int auth,String tableid ,String refinfo,boolean readonly) {
		Log.error("----------------------------------拿到的权限值是"+auth);
		if (StringUtils.equals(tableid,"esign_instance")&&auth==1) {
			return true;
		}

		return false;

	}
    
    public void doAction(HttpServletRequest request, HttpServletResponse response,FuncParamBean fpb) {
		request.getSession().setAttribute("isNewSign","yes");
		this.redirectByUri(request, response,"/LightpdfSignIntergrate.signCreate.do");
		return;
    }

    public FuncInfoBean getFIB(String tableid) {
		FuncInfoBean fib = new FuncInfoBean();
		try{
			fib.setName("创建签字");
			fib.setType(FuncInfoBean.FUNCTYPE_TOPMASKDIV);
			fib.setWinHeight(800);
			fib.setWinWidth(1000);
			fib.setSimpleViewShow(true);
		} catch (Exception e) {
			Log.error("",e);
		}
		return fib;
	}
    
}
