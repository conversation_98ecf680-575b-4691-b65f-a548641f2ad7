package net.bioknow.cdtms.lightpdfSign;

import net.bioknow.services.core.ApiResult;
import net.bioknow.services.uap.dbdatamng.function.DTTableFuncActionNew;
import net.bioknow.services.uap.dbdatamng.function.FuncInfoBeanNew;
import net.bioknow.services.uap.dbdatamng.function.FuncParamBeanNew;
import net.bioknow.uap.dbdatamng.function.FuncInfoBean;
import net.bioknow.webutil.tools.Log;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;

public class DTTFActionSignVue extends DTTableFuncActionNew {

    public boolean canUse(int auth, String tableid, String refinfo, boolean readonly) {
        Log.error("----------------------------------拿到的权限值是"+auth);
        if (StringUtils.equals(tableid, "esign_instance")) {
            return true;
        }

        return false;

    }


    public void doAction(HttpServletRequest request, HttpServletResponse response, FuncParamBeanNew fpb) {

        try {
            HashMap map = new HashMap();
            request.getSession().setAttribute("isNewSign","yes");
            map.put("url", "/LightpdfSignIntergrate.signCreate.do");
            response.getOutputStream().write(ApiResult.ok("ok", map).getBytes(StandardCharsets.UTF_8));
            response.getOutputStream().close();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }


        return;
    }

    public FuncInfoBeanNew getFIB(String tableid) {
        FuncInfoBeanNew fib = new FuncInfoBeanNew("cdtms_DTTFActionSignVue");
        try {
            fib.setName("创建签字");
            fib.setType(FuncInfoBeanNew.FUNCTYPE_TOPMASKDIV);
            fib.setWinHeight(800);
            fib.setWinWidth(1000);
            fib.setSimpleViewShow(true);
            fib.setAppendParams(false);

        } catch (Exception e) {
            Log.error("", e);
        }
        return fib;
    }

}
